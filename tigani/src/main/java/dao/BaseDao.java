package dao;

import static com.mongodb.MongoClient.getDefaultCodecRegistry;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.Accumulators;
import static com.mongodb.client.model.Accumulators.sum;
import com.mongodb.client.model.Aggregates;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Aggregates.unwind;
import com.mongodb.client.model.Filters;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import core.Core;
import enums.LogType;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.codecs.configuration.CodecProvider;
import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.ClassModel;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.DocumentDescriptor;
import pojo.QueryOptions;
import pojo.User;
import utils.Defaults;
import utils.FileUtils;
import utils.FileUtils.FileType;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class BaseDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseDao.class.getName());

    public static <T> T getDocumentById(ObjectId id, Class<T> objectClass, String language) throws Exception {
        return getDocumentById(id, objectClass, language, true);
    }

    public static <T> T getDocumentById(ObjectId id, Class<T> objectClass) throws Exception {
        return getDocumentById(id, objectClass, null, false);
    }

    public static <T> T getDocumentById(ObjectId id, Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (id == null) {
            throw new InvalidParameterException("id is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("_id", id), ne("cancelled", true), ne("archived", true)), objectClass).first();
    }

    public static User loadUserByRegistrationToken(String registrationToken) throws Exception {
        if (StringUtils.isBlank(registrationToken)) {
            throw new InvalidParameterException("empty registrationToken");
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(
                ne("cancelled", true),
                eq("registrationToken", registrationToken)
        )).first();

        User user = null;
        if (doc != null) {
            user = Core.fromDocument(doc, User.class);
        }
        return user;
    }

    public static <T> T getArchivedDocumentById(ObjectId id, Class<T> objectClass, String language) throws Exception {
        return getArchivedDocumentById(id, objectClass, language, true);
    }

    public static <T> T getArchivedDocumentById(ObjectId id, Class<T> objectClass) throws Exception {
        return getArchivedDocumentById(id, objectClass, null, false);
    }

    public static <T> T getArchivedDocumentById(ObjectId id, Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (id == null) {
            throw new InvalidParameterException("id is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("_id", id), ne("cancelled", true), eq("archived", true)), objectClass).first();
    }

    public static <T> T getDocumentByIdentifier(String identifier, Class<T> objectClass, String language) throws Exception {
        return getDocumentByIdentifier(identifier, objectClass, language, true);
    }

    public static <T> T getDocumentByIdentifier(String identifier, Class<T> objectClass) throws Exception {
        return getDocumentByIdentifier(identifier, objectClass, null, false);
    }

    public static <T> T getDocumentByIdentifier(String identifier, Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (StringUtils.isBlank(identifier)) {
            throw new InvalidParameterException("identifier is blank. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("identifier", identifier), ne("cancelled", true), ne("archived", true)), objectClass).first();
    }

    public static <T> T getDocumentByKey(String key, Class<T> objectClass, String language) throws Exception {
        return getDocumentByKey(key, objectClass, language, true);
    }

    public static <T> T getDocumentByKey(String key, Class<T> objectClass) throws Exception {
        return getDocumentByKey(key, objectClass, null, false);
    }

    public static <T> T getDocumentByKey(String key, Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (StringUtils.isBlank(key)) {
            throw new InvalidParameterException("key is blank. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("key", key), ne("cancelled", true), ne("archived", true)), objectClass).first();
    }

    public static <T> T getDocumentByParentId(String parentId, Class<T> objectClass, String language) throws Exception {
        return getDocumentByParentId(parentId, objectClass, language, true);
    }

    public static <T> T getDocumentByParentId(String parentId, Class<T> objectClass) throws Exception {
        return getDocumentByParentId(parentId, objectClass, null, false);
    }

    public static <T> T getDocumentByParentId(String parentId, Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (StringUtils.isBlank(parentId)) {
            throw new InvalidParameterException("identifier is blank. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(eq("parentId", parentId), ne("cancelled", true), ne("archived", true)), objectClass).first();
    }

    // return a pojo, single entity, no language (settings)
    public static <T> T getDocumentByClass(Class<T> objectClass) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(ne("cancelled", true), ne("archived", true)), objectClass).first();
    }

    public static <T> List<T> getDocumentsByClass(Class<T> objectClass) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(ne("cancelled", true), ne("archived", true)), objectClass).into(new ArrayList<>());
    }

    public static <T> T getDocumentByFilters(Class<T> objectClass, QueryOptions queryOptions, String language) throws Exception {
        return getDocumentByFilters(objectClass, queryOptions, language, true);
    }

    public static <T> T getDocumentByFilters(Class<T> objectClass, QueryOptions queryOptions) throws Exception {
        return getDocumentByFilters(objectClass, queryOptions, null, false);
    }

    public static <T> T getDocumentByFilters(Class<T> objectClass, QueryOptions queryOptions, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        Bson filter = queryOptions.getFilter();
        List<Bson> filters = new ArrayList<>();
        if (filter != null) {
            filters.add(filter);
        }
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

        return collection.find(and(filters))
                .sort(queryOptions.getSort())
                .limit(Math.max(queryOptions.getLimit(), 0)).first();
    }
    
    public static ObjectId insertDocument(Object objectClass, String language) throws Exception {
        return insertDocument(objectClass, language, true);
    }

    public static ObjectId insertDocument(Object objectClass) throws Exception {
        return insertDocument(objectClass, null, false);
    }

    public static ObjectId insertDocument(Object objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        // class should be an extends of BasePojo, so we should be able to set creation and lastUpdate
        // need to check if this is slow due to the reflection... should not
        try {
            Date now = new Date();
            PropertyUtils.setSimpleProperty(objectClass, "creation", now);
            PropertyUtils.setSimpleProperty(objectClass, "lastUpdate", now);
        } catch (IllegalArgumentException | SecurityException ex) {
            LOGGER.error("Unable to set default values creation and lastUpdate.", ex);
        }

        String className = objectClass.getClass().getSimpleName();
        String collectionName = className;
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        Document doc = Core.toDocument(objectClass);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static boolean insertDocuments(List<?> objectClass, String language) throws Exception {
        return insertDocuments(objectClass, language, true);
    }

    public static boolean insertDocuments(List<?> objectClass) throws Exception {
        return insertDocuments(objectClass, null, false);
    }

    public static boolean insertDocuments(List<?> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (objectClass.isEmpty()) {
            throw new InvalidParameterException("table empty. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        // class should be an extends of BasePojo, so we should be able to set creation and lastUpdate
        // need to check if this is slow due to the reflection... should not
        try {
            Date now = new Date();
            for (Object document : objectClass) {
                PropertyUtils.setSimpleProperty(document, "creation", now);
                PropertyUtils.setSimpleProperty(document, "lastUpdate", now);
            }
        } catch (IllegalArgumentException | SecurityException ex) {
            LOGGER.error("Unable to set default values creation and lastUpdate.", ex);
        }

        String className = objectClass.get(0).getClass().getSimpleName();
        String collectionName = className;
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        List<Document> docs = new ArrayList<>();
        for (Object document : objectClass) {
            docs.add(Core.toDocument(document));
        }
        collection.insertMany(docs);
        return true;
    }

    public static void updateDocument(Object objectClass, String language) throws Exception {
        updateDocument(objectClass, language, true);
    }

    public static void updateDocument(Object objectClass) throws Exception {
        updateDocument(objectClass, null, false);
    }

    public static void updateDocument(Object objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        // class should be an extends of BasePojo, so we should be able to set lastUpdate
        // need to check if this is slow due to the reflection... should not
        try {
            Date now = new Date();
            PropertyUtils.setSimpleProperty(objectClass, "lastUpdate", now);
            String className = objectClass.getClass().getSimpleName();
            String collectionName = className;
            if (StringUtils.isNotBlank(language)) {
                collectionName += "_" + language;
            }

            MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
            collection.replaceOne(
                    new Document("_id", PropertyUtils.getSimpleProperty(objectClass, "id")),
                    Core.toDocument(objectClass)
            );
        } catch (IllegalArgumentException | SecurityException ex) {
            LOGGER.error("Error on updateDocument.", ex);
        }
    }

    public static void deleteDocument(Object objectClass, String language) throws Exception {
        deleteDocument(objectClass, language, true);
    }

    public static void deleteDocument(Object objectClass) throws Exception {
        deleteDocument(objectClass, null, false);
    }

    public static void deleteDocument(Object objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        // class should be an extends of BasePojo, so we should be able to set cancelled
        // need to check if this is slow due to the reflection... should not
        try {
            PropertyUtils.setSimpleProperty(objectClass, "cancelled", true);

            // prima devo aggiornare le altre entità se necessario (gestione availableLanguages)
            List<String> availableLanguages = null;
            Field field;
            try {
                field = objectClass.getClass().getSuperclass().getDeclaredField("availableLanguages");
            } catch (NoSuchFieldException e) {
                field = null;
            }
            if (field != null) {
                field.setAccessible(true);
                if (field.get(objectClass) != null) {
                    if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                        availableLanguages = (List<String>) field.get(objectClass);
                        if (field.get(objectClass) != null) {
                            if (manageLanguage) {
                                availableLanguages.remove(language);
                            }
                        }
                        if (availableLanguages.isEmpty()) { // inutile tenere un array vuoto a database
                            field.set(objectClass, null);
                        } else {
                            field.set(objectClass, availableLanguages);
                        }
                    }
                }
            }
            if (availableLanguages != null && !availableLanguages.isEmpty()) {
                String parentId = null;
                field = objectClass.getClass().getSuperclass().getDeclaredField("parentId");
                if (field != null) {
                    field.setAccessible(true);
                    if (field.get(objectClass) != null) {
                        parentId = (String) field.get(objectClass);
                    }
                }

                if (StringUtils.isNotBlank(parentId)) {
                    for (String languageToUpdate : availableLanguages) {
                        Object articleToUpdate = getDocumentByParentId(parentId, objectClass.getClass(), languageToUpdate);
                        field = articleToUpdate.getClass().getSuperclass().getDeclaredField("availableLanguages");
                        if (field != null) {
                            field.setAccessible(true);
                            if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                                field.set(articleToUpdate, availableLanguages);

                                field = articleToUpdate.getClass().getSuperclass().getDeclaredField("language");
                                if (field != null) {
                                    field.setAccessible(true);
                                    String articleToUpdateLanguage = (String) field.get(articleToUpdate);
                                    if (StringUtils.isNotBlank(articleToUpdateLanguage)) {
                                        BaseDao.updateDocument(articleToUpdate, articleToUpdateLanguage);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            updateDocument(objectClass, language, manageLanguage);
        } catch (IllegalArgumentException | NoSuchFieldException | SecurityException ex) {
            LOGGER.error("Error on deleteDocument.", ex);
        }
    }

    public static void deleteCollection(Class<?> objectClass, String language) throws Exception {
        deleteCollection(objectClass, language, true);
    }

    public static void deleteCollection(Class<?> objectClass) throws Exception {
        deleteCollection(objectClass, null, false);
    }

    public static void deleteCollection(Class<?> objectClass, String language, boolean manageLanguage) throws Exception {
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        collection.drop();
    }

    public static ObjectId insertLog(User user, Object objectClass, LogType logType) {
        try {
            if (BooleanUtils.isTrue(Defaults.ENABLE_QUERY_LOGS)) {
                if (user == null) {
                    throw new InvalidParameterException("user is null. Can't continue");
                }
                if (objectClass == null) {
                    throw new InvalidParameterException("objectClass is null. Can't continue");
                }
                if (logType == null) {
                    throw new InvalidParameterException("logType is null. Can't continue");
                }

                MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase("logs"));
                Document doc = new Document();
                doc.append("userId", user.getId());
                doc.append("entity", objectClass.getClass().getSimpleName());
                Field field = objectClass.getClass().getSuperclass().getDeclaredField("id");
                ObjectId entityId = null;
                if (field != null) {
                    field.setAccessible(true);
                    entityId = (ObjectId) field.get(objectClass);
                }
                doc.append("entityId", entityId);
                doc.append("operation", logType.toString());
                doc.append("date", new Date());
                collection.insertOne(doc);
                return doc.get("_id", ObjectId.class);
            } else {
                return null;
            }
        } catch (IllegalArgumentException | NoSuchFieldException | SecurityException | IllegalAccessException ex) {
            LOGGER.error("Error on insertLog.", ex);
        }
        return null;
    }

    public static <T> List<T> getDocuments(Class<T> objectClass, String language) throws Exception {
        return getDocuments(objectClass, 0, language, true);
    }

    public static <T> List<T> getDocuments(Class<T> objectClass) throws Exception {
        return getDocuments(objectClass, 0, null, false);
    }

    public static <T> List<T> getDocuments(Class<T> objectClass, int limit, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        return collection.find(and(ne("cancelled", true), ne("archived", true)), objectClass).limit(limit > 0 ? limit : 0).into(new ArrayList<>());
    }

    public static <T> List<T> getDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, String language, Boolean loadArchived) throws Exception {
        return getDocumentsByFilters(objectClass, queryOptions, language, true, loadArchived);
    }

    public static <T> List<T> getDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, String language) throws Exception {
        return getDocumentsByFilters(objectClass, queryOptions, language, true, false);
    }

    public static <T> List<T> getDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, Boolean loadArchived) throws Exception {
        return getDocumentsByFilters(objectClass, queryOptions, null, false, loadArchived);
    }

    public static <T> List<T> getDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions) throws Exception {
        return getDocumentsByFilters(objectClass, queryOptions, null, false, false);
    }

    public static <T> List<T> getDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, String language, boolean manageLanguage, Boolean loadArchived) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        Bson filter = queryOptions.getFilter();
        List<Bson> filters = new ArrayList<>();
        if (filter != null) {
            filters.add(filter);
        }
        filters.add(ne("cancelled", true));
        if (BooleanUtils.isNotTrue(loadArchived)) {
            filters.add(ne("archived", true));
        }

        return collection.find(and(filters))
            .sort(queryOptions.getSort())
            .skip(queryOptions.getSkip() > 0 ? queryOptions.getSkip() : 0)
            .limit(queryOptions.getLimit() > 0 ? queryOptions.getLimit() : 0)
            .into(new ArrayList<>());

    }
    
    public static <T> List<T> getDistinctDocumentsByField(Class<T> objectClass, String fieldName, QueryOptions queryOptions, String language) throws Exception {
        if (objectClass == null || StringUtils.isBlank(fieldName)) {
            throw new InvalidParameterException("Invalid parameters for distinct documents.");
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<T> collection = Core.mongoDatabase
            .getCollection(StringUtils.lowerCase(collectionName), objectClass)
            .withCodecRegistry(pojoCodecRegistry);

        List<Bson> pipeline = new ArrayList<>();
        
        List<Bson> filters = new ArrayList<>();
        if (queryOptions != null && queryOptions.getFilter() != null) {
            filters.add(queryOptions.getFilter());
        }        
        
        if (!filters.isEmpty()) {
            pipeline.add(Aggregates.match(Filters.and(filters)));
        }

        pipeline.add(Aggregates.group("$" + fieldName, Accumulators.first("doc", "$$ROOT")));
        pipeline.add(Aggregates.replaceRoot("$doc"));

        return collection.aggregate(pipeline).into(new ArrayList<>());
    }
    
    public static <T> Long countDocuments(Class<T> objectClass, String language) throws Exception {
        return countDocuments(objectClass, language, true);
    }

    public static <T> Long countDocuments(Class<T> objectClass) throws Exception {
        return countDocuments(objectClass, null, false);
    }

    public static <T> Long countDocuments(Class<T> objectClass, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        return collection.countDocuments(and(ne("cancelled", true), ne("archived", true)));
    }

    public static <T> Long countDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, String language) throws Exception {
        return countDocumentsByFilters(objectClass, queryOptions, language, true);
    }

    public static <T> Long countDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions) throws Exception {
        return countDocumentsByFilters(objectClass, queryOptions, null, false);
    }

    public static <T> Long countDocumentsByFilters(Class<T> objectClass, QueryOptions queryOptions, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        Bson filter = queryOptions.getFilter();
        List<Bson> filters = new ArrayList<>();
        if (filter != null) {
            filters.add(filter);
        }
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

        return collection.countDocuments(and(filters));
    }

    public static <T> List<T> getArchivedDocuments(Class<T> objectClass, String language) throws Exception {
        return getArchivedDocuments(objectClass, 0, language, true);
    }

    public static <T> List<T> getArchivedDocuments(Class<T> objectClass) throws Exception {
        return getArchivedDocuments(objectClass, 0, null, false);
    }

    public static <T> List<T> getArchivedDocuments(Class<T> objectClass, int limit, String language, boolean manageLanguage) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }
        if (language == null && manageLanguage) { // accetto stringa vuota per le tabelle che non devono essere tradotte
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ClassModel<T> model = ClassModel.builder(objectClass).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }
        MongoCollection<T> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName), objectClass).withCodecRegistry(pojoCodecRegistry);

        return collection.find(and(ne("cancelled", true), eq("archived", true)), objectClass).limit(limit > 0 ? limit : 0).into(new ArrayList<>());
    }

    public static List<ObjectId> saveImages(List<UploadedFile> images, Object objectClass, String fieldName) throws Exception {
        List<ObjectId> imageIds = new ArrayList<>();
        for (UploadedFile file : images) {
            saveImage(file, objectClass, fieldName, false);
        }

        if (!images.isEmpty() && objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");
            if (StringUtils.isNotBlank(language)) {
                updateDocument(objectClass, language);
            } else {
                updateDocument(objectClass);
            }
        }

        return imageIds;
    }

    public static ObjectId saveImage(UploadedFile image, Object objectClass, String fieldName, boolean updateDocument) throws Exception {
        Date now = new Date();

        ObjectId imageId = null;
        try {
            // if image is edited, it lose it's extension, so i put it to "png"
            if (StringUtils.isBlank(image.getExtension())) {
                image.setExtension("png");
            }

            // gestione compressione immagini
            DocumentDescriptor descriptor = new DocumentDescriptor();
            descriptor.setFilename("temporary");
            descriptor.setContent(image.getContent());
            descriptor.setMetadata(new HashMap<>());
            descriptor.getMetadata().put("contentType", image.getContentType());
            FileUtils.compressImage(descriptor);
            if (descriptor.getMetadata() != null && descriptor.getMetadata().containsKey("contentType")) {
                image.setContentType(descriptor.getMetadata().get("contentType").toString());
                String contentType = image.getContentType();
                if (StringUtils.isNotBlank(contentType)) {
                    if (contentType.contains("/")) {
                        image.setExtension(contentType.split("/")[1]);
                    }
                }
            }

            // defaults
            String imageName = FileUtils.composeFilename(FileType.img, FilenameUtils.getBaseName(image.getName()), image.getExtension());

            // save image
            File savedFile = new File(FileUtils.getPath(FileType.img, now, imageName));
            savedFile.getParentFile().mkdirs();
            try ( OutputStream out = new FileOutputStream(savedFile)) {
                out.write(descriptor.getContent());
            }

            // save image
            imageId = uploadFile(savedFile,
                    image.getName(),
                    image.getContentType()
            );

            if (imageId != null) {
                if (objectClass != null) {  // you can also upload a file without assign to an object
                    Field field = objectClass.getClass().getDeclaredField(fieldName);
                    if (field != null) {
                        field.setAccessible(true);
                        if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                            List<ObjectId> temporaryFileIds = (List<ObjectId>) field.get(objectClass);
                            if (field.get(objectClass) == null) {
                                temporaryFileIds = new ArrayList<>();
                            }
                            temporaryFileIds.add(imageId);
                            field.set(objectClass, temporaryFileIds);
                        } else { // altrimenti dovrei solo avere un singolo ObjectId
                            field.set(objectClass, imageId);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Unable to save file " + image.getName(), ex);
        }

        if (objectClass != null && updateDocument) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");
            if (StringUtils.isNotBlank(language)) {
                updateDocument(objectClass, language);
            } else {
                updateDocument(objectClass);
            }
        }

        return imageId;
    }

    public static void deleteImages(Object objectClass, String fieldName) throws Exception {
        if (objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");

            Field field = objectClass.getClass().getDeclaredField(fieldName);
            if (field != null) {
                field.setAccessible(true);
                if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                    List<ObjectId> imagesToDelete = (List<ObjectId>) field.get(objectClass);
                    if (imagesToDelete != null && !imagesToDelete.isEmpty()) {
                        for (ObjectId oid : imagesToDelete) {
                            DocumentDescriptor document = getDocumentById(oid, DocumentDescriptor.class, language);
                            if (document != null) {
                                File file = new File(document.getFilePath());
                                if (file.exists()) {
                                    file.delete();
                                }
                                deleteDocument(document, language);
                            }
                        }
                    }

                    field.set(objectClass, new ArrayList<>());
                    updateDocument(objectClass, language);
                }
            }
        }
    }

    public static void deleteImage(Object objectClass, String fieldName) throws Exception {
        if (objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");

            Field field = objectClass.getClass().getDeclaredField(fieldName);
            if (field != null) {
                field.setAccessible(true);
                ObjectId oid = (ObjectId) field.get(objectClass);

                if (oid != null) {
                    DocumentDescriptor document = getDocumentById(oid, DocumentDescriptor.class, language);
                    if (document != null) {
                        File file = new File(document.getFilePath());
                        if (file.exists()) {
                            file.delete();
                        }
                        deleteDocument(document, language);
                    }

                    field.set(objectClass, null);
                    updateDocument(objectClass, language);
                }
            }
        }
    }

    public static List<ObjectId> saveFiles(List<UploadedFile> files, Object objectClass, String fieldName) throws Exception {
        List<ObjectId> imageIds = new ArrayList<>();
        for (UploadedFile file : files) {
            saveFile(file, objectClass, fieldName, false);
        }

        if (!files.isEmpty() && objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");
            if (StringUtils.isNotBlank(language)) {
                updateDocument(objectClass, language);
            } else {
                updateDocument(objectClass);
            }
        }

        return imageIds;
    }

    public static ObjectId saveFile(UploadedFile file, Object objectClass, String fieldName, boolean updateDocument) throws Exception {
        Date now = new Date();

        ObjectId fileId = null;
        try {
            // if image is edited, it lose it's extension, so i put it to "png"
            /*if (StringUtils.isBlank(file.getExtension())) {
                file.setExtension("png");
            }*/

            // gestione compressione immagini
            DocumentDescriptor descriptor = new DocumentDescriptor();
            descriptor.setFilename("temporary");
            descriptor.setContent(file.getContent());
            descriptor.setMetadata(new HashMap<>());
            descriptor.getMetadata().put("contentType", file.getContentType());
            FileUtils.compressImage(descriptor);
            if (descriptor.getMetadata() != null && descriptor.getMetadata().containsKey("contentType")) {
                file.setContentType(descriptor.getMetadata().get("contentType").toString());
                String contentType = file.getContentType();
                if (StringUtils.isNotBlank(contentType)) {
                    if (contentType.contains("/")) {
                        file.setExtension(contentType.split("/")[1]);
                    }
                }
            }

            // defaults
            String imageName = FileUtils.composeFilename(FileType.doc, FilenameUtils.getBaseName(file.getName()), file.getExtension());

            // save image
            File savedFile = new File(FileUtils.getPath(FileType.doc, now, imageName));
            savedFile.getParentFile().mkdirs();
            try ( OutputStream out = new FileOutputStream(savedFile)) {
                out.write(descriptor.getContent());
            }

            // save image
            fileId = uploadFile(savedFile,
                    file.getName(),
                    file.getContentType()
            );

            if (fileId != null) {
                if (objectClass != null) {  // you can also upload a file without assign to an object
                    Field field = objectClass.getClass().getDeclaredField(fieldName);
                    if (field != null) {
                        field.setAccessible(true);
                        if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                            List<ObjectId> temporaryFileIds = (List<ObjectId>) field.get(objectClass);
                            if (field.get(objectClass) == null) {
                                temporaryFileIds = new ArrayList<>();
                            }
                            temporaryFileIds.add(fileId);
                            field.set(objectClass, temporaryFileIds);
                        } else { // altrimenti dovrei solo avere un singolo ObjectId
                            field.set(objectClass, fileId);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Unable to save file " + file.getName(), ex);
        }

        if (objectClass != null && updateDocument) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");
            if (StringUtils.isNotBlank(language)) {
                updateDocument(objectClass, language);
            } else {
                updateDocument(objectClass);
            }
        }

        return fileId;
    }

    public static void deleteFiles(Object objectClass, String fieldName) throws Exception {
        if (objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");

            Field field = objectClass.getClass().getDeclaredField(fieldName);
            if (field != null) {
                field.setAccessible(true);
                if (Collection.class.isAssignableFrom(field.getType())) { // controllo che sia una lista
                    List<ObjectId> filesToDelete = (List<ObjectId>) field.get(objectClass);
                    if (filesToDelete != null && !filesToDelete.isEmpty()) {
                        for (ObjectId oid : filesToDelete) {
                            DocumentDescriptor document = getDocumentById(oid, DocumentDescriptor.class, language);
                            if (document != null) {
                                File file = new File(document.getFilePath());
                                if (file.exists()) {
                                    file.delete();
                                }
                                deleteDocument(document, language);
                            }
                        }
                    }

                    field.set(objectClass, new ArrayList<>());
                    updateDocument(objectClass, language);
                }
            }
        }
    }

    public static void deleteFile(Object objectClass, String fieldName) throws Exception {
        if (objectClass != null) {
            // carico lingua se impostata
            String language = (String) PropertyUtils.getProperty(objectClass, "language");

            Field field = objectClass.getClass().getDeclaredField(fieldName);
            if (field != null) {
                field.setAccessible(true);
                ObjectId oid = (ObjectId) field.get(objectClass);

                if (oid != null) {
                    DocumentDescriptor document = getDocumentById(oid, DocumentDescriptor.class, language);
                    if (document != null) {
                        File file = new File(document.getFilePath());
                        if (file.exists()) {
                            file.delete();
                        }
                        deleteDocument(document, language);
                    }

                    field.set(objectClass, null);
                    updateDocument(objectClass, language);
                }
            }
        }
    }

    public static ObjectId uploadFile(File file, String originalFilename, String type) throws Exception {
        if (file == null) {
            throw new InvalidParameterException("empty file");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }

        Map<String, String> metadata = new HashMap<>();
        metadata.put("contentType", type);
        metadata.put("originalFilename", originalFilename);

        Date now = new Date();
        DocumentDescriptor document = new DocumentDescriptor();
        document.setFilename(file.getName());
        document.setFilePath(file.getPath());
        document.setMetadata(metadata);
        document.setCreation(now);
        document.setLastUpdate(now);

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection("documentdescriptor");
        Document doc = Core.toDocument(document);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static <T> List<String> loadTagList(Class<T> objectClass, String language) throws Exception {
        return loadTagList(objectClass, language, null);
    }

    public static <T> List<String> loadTagList(Class<T> objectClass, String language, String company) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

//        if (StringUtils.isNotBlank(language)) {
//            filters.add(eq("language", language));
//        }
        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        unwind("$tags"),
                        group("$tags", sum("count", 1)),
                        project(fields(include("tags"))),
                        sort(orderBy(ascending("tags")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("_id");
                items.add(value);
            }
        }
        return !items.isEmpty() ? items : null;
    }
    
    public static <T> List<String> loadCategoryList(Class<T> objectClass, String language) throws Exception {
        return loadCategoryList(objectClass, language, null);
    }

    public static <T> List<String> loadCategoryList(Class<T> objectClass, String language, String company) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

//        if (StringUtils.isNotBlank(language)) {
//            filters.add(eq("language", language));
//        }
        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        unwind("$category"),
                        group("$category", sum("count", 1)),
                        project(fields(include("category"))),
                        sort(orderBy(ascending("category")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("_id");
                items.add(value);
            }
        }
        return !items.isEmpty() ? items : null;
    }
    
    public static <T> Map<String, String> loadCategoryMap(Class<T> objectClass, String language) throws Exception {
        if (objectClass == null) {
            throw new InvalidParameterException("table is null. Can't continue");
        }

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("archived", true));

        String collectionName = objectClass.getSimpleName();
        if (StringUtils.isNotBlank(language)) {
            collectionName += "_" + language;
        }

        MongoCollection<Document> collection = Core.mongoDatabase.getCollection(StringUtils.lowerCase(collectionName));
        AggregateIterable<Document> list = collection.aggregate(Arrays.asList(
            match(and(filters)),
            project(fields(
                include("category", "categoryIdentifier")
            )),
            sort(orderBy(ascending("category")))
        ));

        Map<String, String> map = new HashMap<>();
        if (list != null) {
            for (Document doc : list) {
                String key = doc.getString("categoryIdentifier");
                String value = doc.getString("category");
                if (key != null && value != null) {
                    map.put(key, value);
                }
            }
        }

        return !map.isEmpty() ? map : null;
    }

    /**
     * Check if a collection exists in the database
     * @param collectionName the name of the collection to check
     * @return true if the collection exists, false otherwise
     */
    public static boolean collectionExists(String collectionName) {
        try {
            MongoIterable<String> collectionNames = Core.mongoDatabase.listCollectionNames();
            for (String name : collectionNames) {
                if (name.equalsIgnoreCase(collectionName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Error checking if collection exists: " + collectionName, ex);
            return false;
        }
    }

    /**
     * Check if a document with the given _id exists in the collection
     */
    public static boolean documentExistsById(String collectionName, ObjectId id) {
        try {
            MongoCollection<Document> collection = Core.mongoDatabase.getCollection(collectionName.toLowerCase());
            Document found = collection.find(new Document("_id", id)).first();
            return found != null;
        } catch (Exception ex) {
            LOGGER.error("Error checking document existence in collection: " + collectionName, ex);
            return false;
        }
    }

    /**
     * Insert a document into the specified collection
     */
    public static void insertDocumentToCollection(String collectionName, Document document) {
        try {
            MongoCollection<Document> collection = Core.mongoDatabase.getCollection(collectionName.toLowerCase());
            collection.insertOne(document);
        } catch (Exception ex) {
            LOGGER.error("Error inserting document into collection: " + collectionName, ex);
        }
    }

}
