package pojo;

/**
 * Static registry of all route permissions in the system.
 * This class defines the permissions required for accessing different routes and functionalities.
 * Each permission is defined as a static Permission object with a unique code, name, and description.
 * 
 * <AUTHOR>
 */
public class RoutesPermission {

    // User Management Permission
    public static final Permission USER_MANAGEMENT = createPermission(
            "USER_MANAGEMENT",
            "Gestione Utenti",
            "Consente la gestione degli utenti del sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione degli account utente"
    );

    // Warranty Management Permission
    public static final Permission WARRANTY_MANAGEMENT = createPermission(
            "WARRANTY_MANAGEMENT",
            "Gestione Garanzie",
            "Consente la gestione delle garanzie, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record delle garanzie"
    );

    // Warranty Details Management Permission
    public static final Permission WARRANTY_DETAILS_MANAGEMENT = createPermission(
            "WARRANTY_DETAILS_MANAGEMENT",
            "Gestione Dettagli Garanzie",
            "Consente la gestione dei dettagli delle garanzie, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei dettagli delle garanzie"
    );

    // Insurance Company Management Permission
    public static final Permission INSURANCE_COMPANY_MANAGEMENT = createPermission(
            "INSURANCE_COMPANY_MANAGEMENT",
            "Gestione Compagnie Assicurative",
            "Consente la gestione delle compagnie assicurative, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record delle compagnie assicurative"
    );

    // Warranty Type Management Permission
    public static final Permission WARRANTY_TYPE_MANAGEMENT = createPermission(
            "WARRANTY_TYPE_MANAGEMENT",
            "Gestione Tipi di Garanzia",
            "Consente la gestione dei tipi di garanzia, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei tipi di garanzia"
    );

    // Insurance Provenance Type Management Permission
    public static final Permission INSURANCE_PROVENANCE_TYPE_MANAGEMENT = createPermission(
            "INSURANCE_PROVENANCE_TYPE_MANAGEMENT",
            "Gestione Tipi di Provenienza Assicurativa",
            "Consente la gestione dei tipi di provenienza assicurativa, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei tipi di provenienza assicurativa"
    );

    // Channel Management Permission
    public static final Permission CHANNEL_MANAGEMENT = createPermission(
            "CHANNEL_MANAGEMENT",
            "Gestione Canali",
            "Consente la gestione dei canali, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei canali"
    );

    // Mail Template Management Permission
    public static final Permission MAIL_TEMPLATE_MANAGEMENT = createPermission(
            "MAIL_TEMPLATE_MANAGEMENT",
            "Gestione Modelli Email",
            "Consente la gestione dei modelli email, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei modelli email"
    );

    // System Administration Permission
    public static final Permission SYSTEM_ADMINISTRATION = createPermission(
            "SYSTEM_ADMINISTRATION",
            "Amministrazione Sistema",
            "Consente l'accesso alle funzioni di amministrazione del sistema, incluse le impostazioni, la manutenzione e la configurazione del sistema"
    );

    // Permission Management Permission
    public static final Permission PERMISSION_MANAGEMENT = createPermission(
            "PERMISSION_MANAGEMENT",
            "Gestione Permessi",
            "Consente la gestione dei permessi di sistema, inclusa la visualizzazione, creazione, modifica ed eliminazione dei record dei permessi e delle assegnazioni dei permessi agli utenti"
    );

    /**
     * Helper method to create Permission objects
     */
    private static Permission createPermission(String code, String name, String description) {
        Permission permission = new Permission();
        permission.setCode(code);
        permission.setName(name);
        permission.setDescription(description);
        return permission;
    }

    /**
     * Get all defined permissions as an array
     * @return Array of all static Permission objects
     */
    public static Permission[] getAllPermissions() {
        return new Permission[] {
            USER_MANAGEMENT,
            WARRANTY_MANAGEMENT,
            WARRANTY_DETAILS_MANAGEMENT,
            INSURANCE_COMPANY_MANAGEMENT,
            WARRANTY_TYPE_MANAGEMENT,
            INSURANCE_PROVENANCE_TYPE_MANAGEMENT,
            CHANNEL_MANAGEMENT,
            MAIL_TEMPLATE_MANAGEMENT,
            SYSTEM_ADMINISTRATION,
            PERMISSION_MANAGEMENT
        };
    }
}
