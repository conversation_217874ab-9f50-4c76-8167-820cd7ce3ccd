package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Permission;
import pojo.RoutesPermission;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 * Controller for managing system permissions
 * 
 * <AUTHOR>
 */
public class PermissionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionController.class.getName());

    public static TemplateViewRoute be_permission_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PERMISSION_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_permission = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("permissionId"));
        if (oid != null) {
            Permission loadedPermission = BaseDao.getDocumentById(oid, Permission.class);
            attributes.put("curPermission", loadedPermission);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Permission loadedPermission = BaseDao.getDocumentByParentId(parentId, Permission.class);
                if (loadedPermission != null) {
                    attributes.put("curPermission", loadedPermission);
                }
            }
        }

        return Core.render(Pages.BE_PERMISSION, attributes, request);
    };

    public static TemplateViewRoute be_permission_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("permissionId"));
        if (oid != null) {
            Permission loadedPermission = BaseDao.getDocumentById(oid, Permission.class);
            attributes.put("curPermission", loadedPermission);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Permission loadedPermission = BaseDao.getDocumentByParentId(parentId, Permission.class);
                if (loadedPermission != null) {
                    attributes.put("curPermission", loadedPermission);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_PERMISSION_FORM, attributes, request);
    };

    public static Route be_permission_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.ne("cancelled", true));
        if (!loadArchived) {
            filters.add(Filters.ne("archived", true));
        } else {
            filters.add(Filters.eq("archived", true));
        }

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
        List<Permission> permissions = BaseDao.getDocumentsByFilters(Permission.class, queryOptions, null, false);

        StringBuilder json = new StringBuilder();
        json.append("{\"data\":[");
        boolean first = true;
        for (Permission permission : permissions) {
            if (!first) {
                json.append(",");
            }
            first = false;

            json.append("{");
            json.append("\"id\":\"").append(permission.getId()).append("\",");
            json.append("\"code\":\"").append(StringUtils.defaultString(permission.getCode())).append("\",");
            json.append("\"name\":\"").append(StringUtils.defaultString(permission.getName())).append("\",");
            json.append("\"description\":\"").append(StringUtils.defaultString(permission.getDescription())).append("\",");
            json.append("\"creation\":\"").append(DateTimeUtils.dateToString(permission.getCreation(), "dd/MM/YYYY")).append("\"");
            json.append("}");
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_permission_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("permissionId"));
        
        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;
        
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), requiredPermission);

        Permission newPermission;
        if (oid != null) {
            newPermission = BaseDao.getDocumentById(oid, Permission.class);
            RequestUtils.mergeFromParams(params, newPermission);
        } else {
            newPermission = RequestUtils.createFromParams(params, Permission.class);
        }

        if (newPermission != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newPermission);
                newPermission.setId(oid);

                BaseDao.insertLog(user, newPermission, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newPermission);
                BaseDao.insertLog(user, newPermission, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_permission_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.PERMISSION_MANAGEMENT.getCode(), PermissionType.DELETE);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String permissionIds = params.get("permissionIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(permissionIds)) {
            String[] ids = permissionIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Permission tmpPermission = BaseDao.getDocumentById(oid, Permission.class);
                    if (tmpPermission != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpPermission);
                                BaseDao.insertLog(user, tmpPermission, LogType.DELETE);
                                break;
                            case "archive":
                                tmpPermission.setArchived(true);
                                BaseDao.updateDocument(tmpPermission);
                                BaseDao.insertLog(user, tmpPermission, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpPermission.setArchived(false);
                                BaseDao.updateDocument(tmpPermission);
                                BaseDao.insertLog(user, tmpPermission, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "OK";
    };

    /**
     * Initialize default permissions in the database
     * This method should be called during system setup
     */
    public static Route be_permission_initialize = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, 
            RoutesPermission.SYSTEM_ADMINISTRATION.getCode(), PermissionType.CREATE);

        try {
            Permission[] defaultPermissions = RoutesPermission.getAllPermissions();
            int created = 0;
            
            for (Permission permission : defaultPermissions) {
                // Check if permission already exists
                List<Permission> existing = BaseDao.getDocumentsByFilters(Permission.class, null, null, false);
                boolean exists = existing.stream().anyMatch(p -> 
                    StringUtils.equals(p.getCode(), permission.getCode()));
                
                if (!exists) {
                    BaseDao.insertDocument(permission);
                    BaseDao.insertLog(user, permission, LogType.INSERT);
                    created++;
                }
            }
            
            return "Initialized " + created + " permissions";
        } catch (Exception ex) {
            LOGGER.error("Error initializing permissions", ex);
            return "Error: " + ex.getMessage();
        }
    };
}
