package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import dao.UserDao;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.QueryOptions;
import pojo.User;
import pojo.UserPermission;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.PasswordHash;
import utils.RequestUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class LoginController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class.getName());
    
    public static TemplateViewRoute be_login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        if (params.containsKey("wrongUsernamePassword")) {
            attributes.put("wrongUsernamePassword", true);
        }

        return Core.render(Pages.BE_LOGIN, attributes, request);
    };

    public static TemplateViewRoute be_dashboard = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_DASHBOARD, attributes, request);
    };

    public static Route be_login_do = (Request request, Response response) -> {
        // TODO: fare parte di be_login -> LoginController con Manager.createSession e Manager.putSession
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String username = params.get("username");
        String password = params.get("password");
        User user = UserDao.loadUserByEmail(username);
        if (user != null) {
            // check password
            if (isPasswordCorrect(user, password)) {

                // Load user permissions
                loadUserPermissions(user);

                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Core.createSession(request, response, token);
                Core.addValueToSession(token, "user", user);

                response.redirect(RoutesUtils.contextPath(request) + Defaults.FIRST_PAGE);
                return null;
            }
        }
        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_LOGIN + "?wrongUsernamePassword=true");
        return null;
    };

    public static TemplateViewRoute be_forgot = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Core.render(Pages.BE_FORGOT, attributes, request);
    };

    public static Route be_forgot_do = (Request request, Response response) -> {
        // TODO
        return false;
    };

    public static TemplateViewRoute be_logout = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user != null) {
            Core.destroySession(request, response);
        }

        return Core.render(Pages.BE_LOGIN, attributes, request);
    };

    public static boolean isPasswordCorrect(User user, String password) {
        try {
            return PasswordHash.validatePassword(password, user.getPassword());
        } catch (NoSuchAlgorithmException | InvalidKeySpecException ex) {
            LOGGER.error("Unable to check user password", ex);
        }

        return false;
    }

    /**
     * Load user permissions from the database and populate the user's permissions map
     * @param user The user to load permissions for
     */
    private static void loadUserPermissions(User user) {
        try {
            if (user != null && user.getId() != null) {
                // Initialize permissions map
                user.initializePermissions();

                List<Bson> filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
                QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
                // Query UserPermission collection for this user
                List<UserPermission> userPermissions = BaseDao.getDocumentsByFilters(
                    UserPermission.class,
                        queryOptions,
                    null,
                    false,
                        false
                );

                // Filter by userId (since BaseDao doesn't have a direct method for this)
                Map<String, List<String>> permissionsMap = new HashMap<>();
                for (UserPermission userPermission : userPermissions) {
                    if (userPermission.getUserId() != null &&
                        userPermission.getUserId().equals(user.getId()) &&
                        userPermission.getPermissionCode() != null &&
                        userPermission.getPermissionType() != null) {

                        String permissionCode = userPermission.getPermissionCode();
                        String permissionType = userPermission.getPermissionType();

                        // Add to permissions map
                        permissionsMap.computeIfAbsent(permissionCode, k -> new ArrayList<>()).add(permissionType);
                    }
                }

                user.setPermissions(permissionsMap);
                LOGGER.info("Loaded {} permission entries for user {}", permissionsMap.size(), user.getEmail());
            }
        } catch (Exception ex) {
            LOGGER.error("Error loading user permissions for user " + (user != null ? user.getEmail() : "null"), ex);
            // Initialize empty permissions map on error
            if (user != null) {
                user.setPermissions(new HashMap<>());
            }
        }
    }
}
