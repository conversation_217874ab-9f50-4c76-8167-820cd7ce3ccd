package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class UserController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class.getName());



    public static TemplateViewRoute be_user_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_USER_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_user = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("userId"));
        if (oid != null) {
            User loadedUser = BaseDao.getDocumentById(oid, User.class);
            attributes.put("curUser", loadedUser);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                User loadedUser = BaseDao.getDocumentByParentId(parentId, User.class);
                if (loadedUser != null) {
                    attributes.put("curUser", loadedUser);
                }
            }
        }

        return Core.render(Pages.BE_USER, attributes, request);
    };

    public static TemplateViewRoute be_user_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("userId"));
        if (oid != null) {
            User loadedUser = BaseDao.getDocumentById(oid, User.class);
            attributes.put("curUser", loadedUser);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                User loadedUser = BaseDao.getDocumentByParentId(parentId, User.class);
                if (loadedUser != null) {
                    attributes.put("curUser", loadedUser);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_USER_FORM, attributes, request);
    };

    public static Route be_user_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<User> loadedUseres;
        List<Bson> orFilters = new ArrayList<>();
        orFilters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.EQ, "customer"));
        orFilters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.EQ, "unconfirmed"));
        
        Bson orFilter = Filters.or(orFilters);
        List<Bson> filters = new ArrayList<>();
        filters.add(orFilter); 

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedUseres = BaseDao.getDocumentsByFilters(User.class, queryOptions, loadArchived);
        } else {
            loadedUseres = BaseDao.getDocumentsByFilters(User.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedUseres.isEmpty()) {
            for (User tmpUser : loadedUseres) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' userId='").append(tmpUser.getId()).append("'>").append((StringUtils.defaultIfBlank(tmpUser.getName(), "N.D."))).append(" ").append((StringUtils.defaultIfBlank(tmpUser.getLastname(), ""))).append("</a>\",");
                json.append("\"").append(tmpUser.getEmail()).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpUser.getPhoneNumber(), "N.D.")).append("\",");
                json.append("\"").append(tmpUser.getProfileType().toLowerCase()).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpUser.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpUser.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\"");
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_user_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("userId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), requiredPermission);

        User newUser;
        if (oid != null) {
            newUser = BaseDao.getDocumentById(oid, User.class);
            RequestUtils.mergeFromParams(params, newUser);
        } else {
            newUser = RequestUtils.createFromParams(params, User.class);
        }

        if (newUser != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newUser);
                newUser.setId(oid);

                BaseDao.insertLog(user, newUser, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newUser);
                BaseDao.insertLog(user, newUser, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newUser, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newUser, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_user_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.DELETE);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String userIds = params.get("userIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(userIds)) {
            String[] ids = userIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    User tmpUser = BaseDao.getDocumentById(oid, User.class);
                    if (tmpUser != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpUser);
                                BaseDao.insertLog(tmpUser, tmpUser, LogType.DELETE);
                                break;
                            case "archive":
                                tmpUser.setArchived(true);
                                BaseDao.updateDocument(tmpUser);
                                BaseDao.insertLog(tmpUser, tmpUser, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpUser.setArchived(false);
                                BaseDao.updateDocument(tmpUser);
                                BaseDao.insertLog(tmpUser, tmpUser, LogType.UPDATE);
                                break;
                            case "confirm":
                                if (StringUtils.equalsIgnoreCase(tmpUser.getProfileType(), ProfileType.UNCONFIRMED.name().toLowerCase())) {
                                    tmpUser.setProfileType(ProfileType.CUSTOMER.name().toLowerCase());
                                    BaseDao.updateDocument(tmpUser);
                                    BaseDao.insertLog(tmpUser, tmpUser, LogType.UPDATE);

                                    // TODO: MANDARE MAIL CONFERMA
                                    break;
                                }
                        }
                    }
                }
            }
        }

        return "ok";
    };

    /**
     * Get user permissions for permission assignment interface
     */
    public static Route be_user_permissions = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId userId = RequestUtils.toObjectId(request.queryParams("userId"));
        if (userId == null) {
            return "{\"error\": \"User ID required\"}";
        }

        try {
            // Get all available permissions
            Permission[] allPermissions = RoutesPermission.getAllPermissions();

            // Get user's current permissions
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, userId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<UserPermission> userPermissions = BaseDao.getDocumentsByFilters(UserPermission.class, queryOptions, null, false, false);
            Map<String, List<String>> userPermissionMap = new HashMap<>();

            for (UserPermission userPermission : userPermissions) {
                if (userPermission.getUserId() != null && userPermission.getUserId().equals(userId)) {
                    userPermissionMap.computeIfAbsent(userPermission.getPermissionCode(), k -> new ArrayList<>())
                        .add(userPermission.getPermissionType());
                }
            }

            StringBuilder json = new StringBuilder();
            json.append("{\"permissions\":[");
            boolean first = true;
            for (Permission permission : allPermissions) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                List<String> userTypes = userPermissionMap.getOrDefault(permission.getCode(), new ArrayList<>());

                json.append("{");
                json.append("\"code\":\"").append(StringUtils.defaultString(permission.getCode())).append("\",");
                json.append("\"name\":\"").append(StringUtils.defaultString(permission.getName())).append("\",");
                json.append("\"description\":\"").append(StringUtils.defaultString(permission.getDescription())).append("\",");
                json.append("\"userPermissions\":{");
                json.append("\"view\":").append(userTypes.contains("view")).append(",");
                json.append("\"create\":").append(userTypes.contains("create")).append(",");
                json.append("\"edit\":").append(userTypes.contains("edit")).append(",");
                json.append("\"delete\":").append(userTypes.contains("delete"));
                json.append("}}");
            }
            json.append("]}");

            return json.toString();
        } catch (Exception ex) {
            LOGGER.error("Error loading user permissions", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };

    /**
     * Save user permissions
     */
    public static Route be_user_permissions_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.EDIT);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        ObjectId userId = RequestUtils.toObjectId(params.get("userId"));
        if (userId == null) {
            return "{\"error\": \"User ID required\"}";
        }

        try {
            // Delete existing user permissions
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, userId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<UserPermission> existingPermissions = BaseDao.getDocumentsByFilters(UserPermission.class, queryOptions, null, false, false);
            for (UserPermission existing : existingPermissions) {
                if (existing.getUserId() != null && existing.getUserId().equals(userId)) {
                    BaseDao.deleteDocument(existing);
                }
            }

            // Add new permissions
            int savedCount = 0;
            for (String key : params.keySet()) {
                if (key.startsWith("permission|")) {
                    String[] parts = key.split("\\|");
                    if (parts.length == 3) {
                        String permissionCode = parts[1];
                        String permissionType = parts[2];

                        if (StringUtils.equals(params.get(key), "true")) {
                            UserPermission userPermission = new UserPermission();
                            userPermission.setUserId(userId);
                            userPermission.setPermissionCode(permissionCode);
                            userPermission.setPermissionType(permissionType);

                            BaseDao.insertDocument(userPermission);
                            BaseDao.insertLog(user, userPermission, LogType.INSERT);
                            savedCount++;
                        }
                    }
                }
            }

            return "{\"success\": true, \"message\": \"Saved " + savedCount + " permissions\"}";
        } catch (Exception ex) {
            LOGGER.error("Error saving user permissions", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };

    /**
     * User Permissions Manager Page - dedicated page for managing user permissions
     */
    public static TemplateViewRoute be_user_permissions_manager = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.EDIT);

        return Core.render(Pages.BE_USER_PERMISSIONS_MANAGER, attributes, request);
    };

    /**
     * Get all users for the permissions manager dropdown
     */
    public static Route be_user_permissions_manager_users = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.USER_MANAGEMENT.getCode(), PermissionType.VIEW);

        try {
            // Get all users non system
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("profileType", DaoFiltersOperation.NE, ProfileType.SYSTEM.name().toLowerCase()));
            QueryOptions query = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<User> users = BaseDao.getDocumentsByFilters(User.class, query, null, false, false);

            StringBuilder json = new StringBuilder();
            json.append("{\"users\":[");
            boolean first = true;
            for (User u : users) {
                if (!first) {
                    json.append(",");
                }
                first = false;

                json.append("{");
                json.append("\"id\":\"").append(u.getId()).append("\",");
                json.append("\"name\":\"").append(StringUtils.defaultString(u.getName())).append("\",");
                json.append("\"lastname\":\"").append(StringUtils.defaultString(u.getLastname())).append("\",");
                json.append("\"email\":\"").append(StringUtils.defaultString(u.getEmail())).append("\",");
                json.append("\"profileType\":\"").append(StringUtils.defaultString(u.getProfileType())).append("\"");
                json.append("}");
            }
            json.append("]}");

            return json.toString();
        } catch (Exception ex) {
            LOGGER.error("Error loading users for permissions manager", ex);
            return "{\"error\": \"" + ex.getMessage() + "\"}";
        }
    };
}