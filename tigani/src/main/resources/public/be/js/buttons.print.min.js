/*!
 * Print button for Buttons and DataTables.
 * © SpryMedia Ltd - datatables.net/license
 */
(n=>{var o,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(t){return n(t,window,document)}):"object"==typeof exports?(o=require("jquery"),r=function(t,e){e.fn.dataTable||require("datatables.net")(t,e),e.fn.dataTable.Buttons||require("datatables.net-buttons")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||o(t),r(t,e),n(e,t,t.document)}:(r(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)})(function(p,m,t){var e=p.fn.dataTable,n=t.createElement("a"),f=function(t){n.href=t;t=n.host;return-1===t.indexOf("/")&&0!==n.pathname.indexOf("/")&&(t+="/"),n.protocol+"//"+t+n.pathname+n.search};return e.ext.buttons.print={className:"buttons-print",text:function(t){return t.i18n("buttons.print","Print")},action:function(t,e,n,o,r){var a=e.buttons.exportData(p.extend({decodeEntities:!1},o.exportOptions)),i=e.buttons.exportInfo(o),s=e.columns(o.exportOptions.columns).nodes().map(function(t){return t.className}).toArray(),u='<table class="'+e.table().node().className+'">';o.header&&(u+="<thead>"+a.headerStructure.map(function(t){return"<tr>"+t.map(function(t){return t?'<th colspan="'+t.colspan+'" rowspan="'+t.rowspan+'">'+t.title+"</th>":""}).join("")+"</tr>"}).join("")+"</thead>"),u+="<tbody>";for(var d=0,c=a.body.length;d<c;d++)u+=((t,e)=>{for(var n="<tr>",o=0,r=t.length;o<r;o++){var a=null==t[o]?"":t[o];n+="<"+e+" "+(s[o]?'class="'+s[o]+'"':"")+">"+a+"</"+e+">"}return n+"</tr>"})(a.body[d],"td");u+="</tbody>",o.footer&&a.footer&&(u+="<tfoot>"+a.footerStructure.map(function(t){return"<tr>"+t.map(function(t){return t?'<th colspan="'+t.colspan+'" rowspan="'+t.rowspan+'">'+t.title+"</th>":""}).join("")+"</tr>"}).join("")+"</tfoot>"),u+="</table>";var l=m.open("","");l?(l.document.close(),l.document.title=i.title,p('style, link[rel="stylesheet"]').each(function(){var t=this.cloneNode(!0);"link"===t.tagName.toLowerCase()&&(t.href=f(t.href)),l.document.head.appendChild(t)}),o.customScripts&&o.customScripts.forEach(function(t){var e=l.document.createElement("script");e.src=t,l.document.getElementsByTagName("head")[0].appendChild(e)}),l.document.body.innerHTML="<h1>"+i.title+"</h1><div>"+(i.messageTop||"")+"</div>"+u+"<div>"+(i.messageBottom||"")+"</div>",p(l.document.body).addClass("dt-print-view"),p("img",l.document.body).each(function(t,e){e.setAttribute("src",f(e.getAttribute("src")))}),o.customize&&o.customize(l,o,e),l.setTimeout(function(){o.autoPrint&&(l.print(),l.close())},1e3),r()):e.buttons.info(e.i18n("buttons.printErrorTitle","Unable to open print view"),e.i18n("buttons.printErrorMsg","Please allow popups in your browser for this site to be able to view the print view."),5e3)},async:100,title:"*",messageTop:"*",messageBottom:"*",exportOptions:{},header:!0,footer:!0,autoPrint:!0,customize:null},e});