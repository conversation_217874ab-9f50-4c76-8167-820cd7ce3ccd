var table;
var baseAjaxLink, lastCallLanguageParam;
const FieldTranslationCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }

        // Used to calculate the default order based on last change
        var totalColumns = document.querySelector('.datatable thead tr:first-child').cells.length;

        // Visibility button default class
        $.fn.dataTable.Buttons.defaults.dom.button.className = 'btn';

        // Date format for sorting
        $.fn.dataTable.moment('DD/MM/YYYY');

        // Setting datatable defaults
        $.extend($.fn.dataTable.defaults, {
            autoWidth: false,
            dom: '<"datatable-header justify-content-center flex-wrap"f<"ms-sm-auto"l><"ms-sm-3"B>><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {
                search: '<span class="me-3">Filtra:</span> <div class="form-control-feedback form-control-feedback-end flex-fill">_INPUT_<div class="form-control-feedback-icon"><i class="ph-magnifying-glass opacity-50"></i></div></div>',
                searchPlaceholder: 'Digita per filtrare...',
                lengthMenu: '<span class="me-3">Mostra:</span> _MENU_',
                paginate: { 'first': 'Primo', 'last': 'Ultimo', 'next': $('html').attr('dir') == 'rtl' ? '&larr;' : '&rarr;', 'previous': $('html').attr('dir') == 'rtl' ? '&rarr;' : '&larr;' }
            },
            lengthMenu: [[25, 100, 250, 500], [25, 100, 250, 500]]
        });

        baseAjaxLink = appRoutes.get("BE_FIELDTRANSLATION_DATA");
        var tableOptions = {
            ajax: {
                url: baseAjaxLink,
                dataType: 'json',
                dataSrc: function (datas) {
                    return datas.data;
                }
            },
            // Set default sorting on the third-to-last column
            order: [[totalColumns - 2, 'desc']],
            // select check first column
            pageLength: 25,
            scrollCollapse: true,
            select: {
                style: 'multi',
                selector: 'td:first-child'
            },
            // last column [+] button on small devices
            responsive: {
                details: {
                    type: 'column',
                    target: -1
                }
            },
            buttons: {
                dom: {
                    button: {
                        className: 'btn btn-light'
                    }
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: '<i class="ph-gear"></i>',
                        className: 'btn btn-light dropdown-toggle',
                        buttons: [
                            /*{
                                text: '<i class="ph-archive me-2"></i>Archivia selezionati',
                                className: 'dropdown-item',
                                action: function (e, dt, node, config) {
                                    _archiveSelectedRows();
                                }
                            },*/
                            {
                                text: '<i class="ph-trash me-2"></i>Elimina selezionati',
                                className: 'dropdown-item',
                                action: function (e, dt, node, config) {
                                    _deleteSelectedRows();
                                }
                            }
                        ]
                    }
                ]
            },
            // column definitions
            columnDefs: [
                // checkbox select
                {
                    targets: 0,
                    width: 100,
                    orderable: false,
                    className: 'select-checkbox'
                },
                // actions button
                {
                    targets: -2,
                    orderable: false,
                    className: 'text-center',
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                },
                // responsive button
                {
                    targets: -1,
                    className: 'control',
                    orderable: false
                }
            ]
        };

        // Initialize table
        table = $('.datatable').DataTable(tableOptions);

        // Reload table on archived checkbox change
        $('#fieldtranslation_archived').on('change', function () {
            _reloadTable();
        });

        // Regenerate translations button
        $('#regenerate-translations').on('click', function() {
            _regenerateTranslations();
        });
    };

    function _renderActionDropdown(row) {
        var fieldTranslationId = $(row[1]).attr('fieldTranslationId');
        return '<div class="d-inline-flex">' +
               '<a href="' + appRoutes.get("BE_FIELDTRANSLATION") + '?fieldTranslationId=' + fieldTranslationId + '" class="btn btn-link btn-sm" title="Modifica">' +
               '<i class="ph-note-pencil"></i>' +
               '</a>' +
               '<div class="dropdown">' +
               '<a href="#" class="btn btn-link btn-sm dropdown-toggle" data-bs-toggle="dropdown">' +
               '<i class="ph-list"></i>' +
               '</a>' +
               '<div class="dropdown-menu dropdown-menu-end">' +
               /*'<a href="#" class="dropdown-item" onclick="_archiveSingleRow(\'' + fieldTranslationId + '\')">' +
               '<i class="ph-archive me-2"></i>Archivia' +
               '</a>' +*/
               '<a href="#" class="dropdown-item" onclick="_deleteSingleRow(\'' + fieldTranslationId + '\')">' +
               '<i class="ph-trash me-2"></i>Elimina' +
               '</a>' +
               '</div>' +
               '</div>' +
               '</div>';
    }

    function _reloadTable(languages) {
        var newLink = baseAjaxLink;
        if (typeof languages !== 'undefined' && languages) {
            if (languages.includes("|")) {
                newLink += "?languages=" + languages;
            } else {
                newLink += "?language=" + languages;
            }
        } else {
            newLink += "?languages=" + $("#allLanguagesElement").attr("languages");
        }
        /*var isArchivedChecked = $("#fieldtranslation_archived:checked").length > 0;
        if (isArchivedChecked) {
            if (!newLink.includes("?")) {
                newLink += "?archived=" + isArchivedChecked;
            } else {
                newLink += "&archived=" + isArchivedChecked;
            }
        }*/
        table.ajax.url(newLink).load();
    }

    /*function _archiveSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) {
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined" && rowIndexes.length > 0) {
                var fieldTranslationIds = "";
                for (var i = 0; i < rowIndexes.length; i++) {
                    var fieldTranslationId = $(table.row(rowIndexes[i]).data()[1]).attr('fieldTranslationId');
                    if (fieldTranslationId) {
                        fieldTranslationIds += fieldTranslationId + ",";
                    }
                }
                fieldTranslationIds = fieldTranslationIds.slice(0, -1);

                const formData = new FormData();
                formData.append('fieldTranslationIds', fieldTranslationIds);
                formData.append('operation', "archive");
                formData.append('fromArchived', $("#fieldtranslation_archived:checked").length > 0);
                if (fieldTranslationIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_FIELDTRANSLATION_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during field translation archive', error);
                        }
                    });
                }
            }
        }
    }*/

    function _deleteSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) {
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined" && rowIndexes.length > 0) {
                var fieldTranslationIds = "";
                for (var i = 0; i < rowIndexes.length; i++) {
                    var fieldTranslationId = $(table.row(rowIndexes[i]).data()[1]).attr('fieldTranslationId');
                    if (fieldTranslationId) {
                        fieldTranslationIds += fieldTranslationId + ",";
                    }
                }
                fieldTranslationIds = fieldTranslationIds.slice(0, -1);

                const formData = new FormData();
                formData.append('fieldTranslationIds', fieldTranslationIds);
                formData.append('operation', "delete");
                /*formData.append('fromArchived', $("#fieldtranslation_archived:checked").length > 0);*/
                if (fieldTranslationIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_FIELDTRANSLATION_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during field translation delete', error);
                        }
                    });
                }
            }
        }
    }

    function _regenerateTranslations() {
        Swal.fire({
            title: 'Rigenera Traduzioni',
            text: 'Vuoi rigenerare tutte le traduzioni usando l\'AI? Questa operazione potrebbe richiedere alcuni minuti.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Sì, rigenera',
            cancelButtonText: 'Annulla',
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33'
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI({
                    message: '<div class="d-flex align-items-center"><div class="spinner-border me-3" role="status"></div>Rigenerazione traduzioni in corso...</div>',
                    css: {
                        backgroundColor: 'transparent',
                        border: '0',
                        color: '#fff'
                    }
                });

                $.ajax({
                    url: appRoutes.get("BE_FIELDTRANSLATION_REGENERATE"),
                    type: 'POST',
                    success: function (response) {
                        $.unblockUI();
                        table.ajax.reload();
                        Swal.fire({
                            title: 'Completato!',
                            text: 'Le traduzioni sono state rigenerate con successo.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function (error) {
                        $.unblockUI();
                        Swal.fire({
                            title: 'Errore!',
                            text: 'Errore durante la rigenerazione delle traduzioni: ' + error.responseText,
                            icon: 'error'
                        });
                        console.error('Error during translation regeneration', error);
                    }
                });
            }
        });
    }

    /*function _archiveSingleRow(fieldTranslationId) {
        _performSingleRowAction(fieldTranslationId, 'archive');
    }*/

    function _deleteSingleRow(fieldTranslationId) {
        _performSingleRowAction(fieldTranslationId, 'delete');
    }

    function _performSingleRowAction(fieldTranslationId, operation) {
        const formData = new FormData();
        formData.append('fieldTranslationIds', fieldTranslationId);
        formData.append('operation', operation);
        /*formData.append('fromArchived', $("#fieldtranslation_archived:checked").length > 0);*/

        $.ajax({
            url: appRoutes.get("BE_FIELDTRANSLATION_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                table.ajax.reload();
                new Noty({
                    text: 'Operazione completata correttamente.',
                    type: 'success',
                    closeWith: ['button']
                }).show();
            },
            error: function (error) {
                new Noty({
                    text: error.responseText,
                    type: 'error',
                    closeWith: ['button']
                }).show();
                console.error('Error during field translation operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    /*window._archiveSingleRow = _archiveSingleRow;*/
    window._deleteSingleRow = _deleteSingleRow;

    return {
        init: function () {
            _componentDatatable();
        },
        reloadTable: _reloadTable,
        /*archiveSelectedRows: _archiveSelectedRows,*/
        deleteSelectedRows: _deleteSelectedRows,
        regenerateTranslations: _regenerateTranslations
    };
}();

document.addEventListener('DOMContentLoaded', function () {
    FieldTranslationCollection.init();
});
