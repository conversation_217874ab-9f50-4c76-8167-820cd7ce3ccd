const FieldTranslationSettings = function () {
    // Initialization of components
    const init = function () {
        _componentSelect2();
        _componentValidate();
        _componentMaxlength();
    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });
    };

    // Validation
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize validation
        var validator = $('#fieldtranslation-edit').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },

            // Different components require proper error label placement
            errorPlacement: function(error, element) {

                // Unstyled checkboxes, radios
                if (element.parents().hasClass('form-check')) {
                    error.appendTo( element.parents('.form-check').parent() );
                }

                // Input with icons and Select2
                else if (element.parents().hasClass('form-group-feedback') || element.hasClass('select2-hidden-accessible')) {
                    error.appendTo( element.parent() );
                }

                // Input group, styled file input
                else if (element.parent().is('.uniform-uploader, .uniform-select') || element.parents().hasClass('input-group')) {
                    error.appendTo( element.parent().parent() );
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            },
            rules: {
                fieldName: {
                    required: true,
                    maxlength: 100
                },
                translation: {
                    required: true,
                    maxlength: 200
                },
                sourceClass: {
                    maxlength: 100
                },
                fieldType: {
                    maxlength: 100
                }
            },
            messages: {
                fieldName: {
                    required: "Il nome del campo è obbligatorio",
                    maxlength: "Il nome del campo non può superare i 100 caratteri"
                },
                translation: {
                    required: "La traduzione è obbligatoria",
                    maxlength: "La traduzione non può superare i 200 caratteri"
                },
                sourceClass: {
                    maxlength: "La classe sorgente non può superare i 100 caratteri"
                },
                fieldType: {
                    maxlength: "Il tipo campo non può superare i 100 caratteri"
                }
            }
        });

        // Reset form
        $('#reset').on('click', function() {
            validator.resetForm();
        });
    };

    // Maxlength
    const _componentMaxlength = function() {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.form-control-maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: "badge bg-warning",
            limitReachedClass: "badge bg-danger"
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
document.addEventListener('DOMContentLoaded', function () {
    FieldTranslationSettings.init();
    submitFieldTranslation();
});

function submitFieldTranslation() {
    // Form submission handling
    $('#fieldtranslation-edit').submit(function (e) {
        if ($('#fieldtranslation-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            $.blockUI({
                message: '<div class="d-flex align-items-center"><div class="spinner-border me-3" role="status"></div>Salvataggio in corso...</div>',
                css: {
                    backgroundColor: 'transparent',
                    border: '0',
                    color: '#fff'
                }
            });

            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_FIELDTRANSLATION") + "?fieldTranslationId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during field translation save/update', error);
                }
            });
        }
    });
}
