var table;
var baseAjaxLink, lastCallLanguageParam;
const BusinessCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        if (!$().DataTable) {
            console.warn('Warning - datatables.min.js is not loaded.');
            return;
        }

        // Used to calculate the default order based on last change
        var totalColumns = document.querySelector('.datatable thead tr:first-child').cells.length;

        // Visibility button default class
        $.fn.dataTable.Buttons.defaults.dom.button.className = 'btn';

        // Date format for sorting
        $.fn.dataTable.moment('DD/MM/YYYY');

        // Setting datatable defaults
        $.extend($.fn.dataTable.defaults, {
            autoWidth: false,
            dom: '<"datatable-header justify-content-center flex-wrap"f<"ms-sm-auto"l><"ms-sm-3"B>><"datatable-scroll-wrap"t><"datatable-footer"ip>',
            language: {search: "<span class='me-3'>Cerca:</span> <div class='form-control-feedback form-control-feedback-end flex-fill'>_INPUT_<div class='form-control-feedback-icon'><i class='ph-magnifying-glass opacity-50'></i></div></div>", searchPlaceholder: "Digita per cercare...", lengthMenu: "<span class='me-3'>Mostra:</span> _MENU_", paginate: {first: "Primo", last: "Ultimo", next: "Successivo", previous: "Precedente"}, info: "Mostra da _START_ a _END_ di _TOTAL_ elementi", infoEmpty: "Mostra 0 a 0 di 0 elementi", infoFiltered: "(filtrati da _MAX_ elementi totali)", zeroRecords: "Nessun record corrispondente trovato", emptyTable: "Nessun dato disponibile nella tabella", loadingRecords: "Caricamento...", processing: "Elaborazione...", aria: {sortAscending: ": attiva per ordinare la colonna in ordine crescente", sortDescending: ": attiva per ordinare la colonna in ordine decrescente"}, buttons: {copyTitle: 'Aggiunto negli appunti', copySuccess: {_: '%d righe copiate', 1: '1 riga copiata'}, copy: "Copia", colvis: "Visibilità colonna"}, select: {rows: {_: "Hai selezionato %d righe", 0: "", 1: "Una riga selezionata"}}},
            lengthMenu: [[25, 100, 250, 500], [25, 100, 250, 500]] // Opzioni personalizzate per il menu a tendina
        });


        baseAjaxLink = appRoutes.get("BE_BUSINESS_DATA");
        var tableOptions = {
            ajax: {
                url: baseAjaxLink,
                dataType: 'json',
                dataSrc: function (datas) {
                    return datas.data;
                }
            },
            // Set default sorting on the third-to-last column don't remove!
            order: [[totalColumns - 2, 'desc']],
            // select check first column
            pageLength: 25, // Numero di elementi visualizzati per pagina di default            

            scrollCollapse: true,
            select: {
                style: 'multi',
                selector: 'td:first-child'
            },
            // last column [+] button on small devices
            responsive: {
                details: {
                    type: 'column',
                    target: -1
                }
            },
            // export and columns visibility
            buttons: {
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Azioni',
                        className: 'btn btn-light dropdown-toggle actions-button',
                        enabled: false,
                        buttons: [
                            {
                                text: '<i class="ph-checks me-2"></i> Conferma Attività',
                                action: function (e, dt, node, config) {
                                    // Aggiungi qui la logica per "Conferma"
                                    _confirmSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-x-circle me-2"></i> Rifiuta Attività',
                                action: function (e, dt, node, config) {
                                    // Aggiungi qui la logica per "Rifiuta"
                                    _rejectSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-star me-2"></i> Metti in Evidenza',
                                action: function (e, dt, node, config) {
                                    _setPreferredSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-star-half me-2"></i> Togli da Evidenza',
                                action: function (e, dt, node, config) {
                                    _setUnPreferredSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-trash me-2"></i> Archivia',
                                action: function (e, dt, node, config) {
                                    // Aggiungi qui la logica per "Archivia"
                                    _archiveSelectedRows();
                                }
                            },
                            {
                                text: '<i class="ph-x me-2"></i> Elimina',
                                action: function (e, dt, node, config) {
                                    // Aggiungi qui la logica per "Elimina"
                                    _deleteSelectedRows();
                                }
                            }
                        ]
                    },
                    {
                        extend: 'excelHtml5',
                        title: document.title,
                        className: 'btn btn-light',
                        orientation: 'landscape',
                        pageSize: 'A4',
                        exportOptions: {
                            columns: ':visible:not(.select-checkbox)',
                            format: {
                                body: function (data, row, column, node) {
                                    // Rimuovi i tag HTML dai badge per l'export
                                    return data.replace(/<[^>]*>/g, '');
                                }
                            }
                        }
                    },
                    {
                        extend: 'pdfHtml5',
                        title: document.title,
                        className: 'btn btn-light',
                        exportOptions: {
                            columns: ':visible:not(.select-checkbox)',
                            format: {
                                body: function (data, row, column, node) {
                                    // Rimuovi i tag HTML dai badge per l'export
                                    return data.replace(/<[^>]*>/g, '');
                                }
                            }
                        },
                        customize: function (doc) {
                            doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                            doc.pageMargins = [10, 10, 10, 10];
                        }
                    },
                    {
                        extend: 'colvis',
                        text: '<i class="ph-list"></i>',
                        className: 'btn btn-light btn-icon dropdown-toggle'
                    }
                ]
            },
            // save column position after refresh page
            //stateSave: true,
            // column definitions
            columnDefs: [
                // checkbox select don't remove!
                {
                    targets: 0,
                    width: 100,
                    orderable: false,
                    className: 'select-checkbox'
                },
                // Colonna Stato
                {
                    targets: 4,
                    render: function (data, type, row, meta) {
                        if (type === 'display' || type === 'type') {
                            if (data && data.toLowerCase() === 'confirmed') {
                                return '<span class="badge bg-success">Confermato</span>';
                            } else if (data && data.toLowerCase() === 'unconfirmed') {
                                return '<span class="badge bg-warning">Non Confermato</span>';
                            } else if (data && data.toLowerCase() === 'draft') {
                                return '<span class="badge bg-info">In Attesa</span>';
                            } else if (data && data.toLowerCase() === 'rejected') {
                                return '<span class="badge bg-danger">Rifiutato</span>';
                            }
                            return data; // Fallback per valori non riconosciuti
                        }
                        return data; // Per sorting/filtering usa il valore originale
                    }
                },
                // Colonna In Evidenza
                {
                    targets: 5,
                    render: function (data, type, row, meta) {
                        if (type === 'display' || type === 'type') {
                            if (data && (data.toLowerCase() === 'yes' || data.toLowerCase() === 'sì' || data.toLowerCase() === 'si')) {
                                return '<span class="badge bg-success">Sì</span>';
                            } else if (data && data.toLowerCase() === 'no') {
                                return '<span class="badge bg-secondary">No</span>';
                            }
                            return data; // Fallback per valori non riconosciuti
                        }
                        return data; // Per sorting/filtering usa il valore originale
                    }
                },
                // actions button don't remove!
                {
                    targets: -2,
                    orderable: false,
                    className: 'text-center',
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                },
                // responsive button don't remove!
                {
                    targets: -1,
                    className: 'control',
                    orderable: false
                }
            ]
        };

        // Table init
        table = $('.datatable').DataTable(tableOptions);

        // Dopo la configurazione del DataTable
        table.on('select.dt deselect.dt', function () {
            var count = table.rows({selected: true}).count();
            var button = table.button('.actions-button'); // Ottiene il riferimento al pulsante
            button.enable(count > 0); // Abilita o disabilita il pulsante

            // Aggiunge o rimuove la classe btn-primary basato sullo stato del pulsante
            if (count > 0) {
                $(button.node()).removeClass('btn-light');
                $(button.node()).addClass('btn-primary');
            } else {
                $(button.node()).removeClass('btn-primary');
                $(button.node()).addClass('btn-light');
            }

        });

        // Adjust columns on window resize
        setTimeout(function () {
            $(window).on('resize', function () {
                table.columns.adjust();
            });
        }, 100);

    };

    function _renderActionDropdown(row) {
        // Extract business ID from the second column (name column with businessId attribute)
        var businessId = $(row[1]).attr('businessid');
        var rawStatus = row[4]; // Status column (raw data)
        var rawEditorChoice = row[5]; // Editor choice column (raw data)

        // Use raw status data for comparison (before HTML rendering)
        var cleanStatus = rawStatus ? rawStatus.toLowerCase() : '';
        console.log(cleanStatus);
        var isPreferred = rawEditorChoice && (rawEditorChoice.toLowerCase() === 'sì' || rawEditorChoice.toLowerCase() === 'si' || rawEditorChoice.toLowerCase() === 'yes');

        var dropdownId = 'actionDropdown_' + businessId;

        var dropdown = '<div class="dropdown">' +
            '<button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="' + dropdownId + '" data-bs-toggle="dropdown" aria-expanded="false">' +
            '<i class="ph-gear me-1"></i>' +
            '</button>' +
            '<ul class="dropdown-menu" aria-labelledby="' + dropdownId + '">';

        // Confirm action - only for unconfirmed status
        if (cleanStatus === 'unconfirmed') {
            dropdown += '<li><a class="dropdown-item" href="#" onclick="_confirmSingleRow(\'' + businessId + '\'); return false;">' +
                '<i class="ph-checks me-2"></i>Conferma Attività</a></li>';
        }

        // Reject action - only for unconfirmed status
        if (cleanStatus === 'unconfirmed') {
            dropdown += '<li><a class="dropdown-item" href="#" onclick="_rejectSingleRow(\'' + businessId + '\'); return false;">' +
                '<i class="ph-x-circle me-2"></i>Rifiuta Attività</a></li>';
        }

        // Preferred/Unpreferred actions - only for confirmed status
        if (cleanStatus === 'confirmed') {
            if (!isPreferred) {
                dropdown += '<li><a class="dropdown-item" href="#" onclick="_setPreferredSingleRow(\'' + businessId + '\'); return false;">' +
                    '<i class="ph-star me-2"></i>Metti in Evidenza</a></li>';
            } else {
                dropdown += '<li><a class="dropdown-item" href="#" onclick="_setUnPreferredSingleRow(\'' + businessId + '\'); return false;">' +
                    '<i class="ph-star-half me-2"></i>Togli da Evidenza</a></li>';
            }
        }

        // Archive action - available for all
        if (cleanStatus === 'unconfirmed' || cleanStatus === 'confirmed') {
            dropdown += '<li><hr class="dropdown-divider"></li>';
        }
        dropdown += '<li><a class="dropdown-item" href="#" onclick="_archiveSingleRow(\'' + businessId + '\'); return false;">' +
            '<i class="ph-trash me-2"></i>Archivia</a></li>';

        // Delete action - available for all
        dropdown += '<li><a class="dropdown-item text-danger" href="#" onclick="_deleteSingleRow(\'' + businessId + '\'); return false;">' +
            '<i class="ph-x me-2"></i>Elimina</a></li>';

        dropdown += '</ul></div>';

        return dropdown;
    }

    function _reloadTable(languages) {
        // formato languages: [it|en|fr]
        var newLink = baseAjaxLink;
        if (typeof languages !== 'undefined' && languages) {
            if (languages.includes("|")) {
                newLink += "?languages=" + languages;
            } else {
                newLink += "?language=" + languages;
            }
        } else { // se non la passo le prendo tutte (usato per gli archiviati)
            newLink += "?languages=" + $("#allLanguagesElement").attr("languages");
        }
        // check archiviati
        var isArchivedChecked = $("#business_archived:checked").length > 0;
        if (isArchivedChecked) {
            if (!newLink.includes("?")) {
                newLink += "?archived=" + isArchivedChecked;
            } else {
                newLink += "&archived=" + isArchivedChecked;
            }
        }
        table.ajax.url(newLink).load();
    }

    function _confirmSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "confirm");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business archive', error);
                        }
                    });
                }
            }
        }
    }

    function _archiveSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "archive");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business archive', error);
                        }
                    });
                }
            }
        }
    }

    function _rejectSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "reject");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business reject', error);
                        }
                    });
                }
            }
        }
    }

    function _setPreferredSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "preferred");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business preferred', error);
                        }
                    });
                }
            }
        }
    }

    function _setUnPreferredSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "unpreferred");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business unpreferred', error);
                        }
                    });
                }
            }
        }
    }

    function _deleteSelectedRows() {
        if (typeof table !== "undefined") {
            var rowIndexes;
            table.rows({selected: true}).each(function (element) { // dovrebbe essere solo un output
                rowIndexes = element;
            });

            if (typeof rowIndexes !== "undefined") {
                var businessIds = "";
                rowIndexes.forEach(function (element, index) {
                    var domElement = $(table.row(element).data()[1]);
                    if (typeof domElement !== "undefined") {
                        if (businessIds !== "") {
                            businessIds += ",";
                        }
                        businessIds += domElement.attr("businessid");
                    }
                });

                const formData = new FormData();
                formData.append('businessIds', businessIds);
                formData.append('operation', "delete");
                formData.append('fromArchived', $("#business_archived:checked").length > 0);
                if (businessIds !== "") {
                    $.ajax({
                        url: appRoutes.get("BE_BUSINESS_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            table.ajax.reload();
                            new Noty({
                                text: 'Dati salvati correttamente.',
                                type: 'success',
                                closeWith: ['button']
                            }).show();
                        },
                        error: function (error) {
                            // Handle errors
                            new Noty({
                                text: error.responseText,
                                type: 'error',
                                closeWith: ['button']
                            }).show();
                            console.error('Error during business archive', error);
                        }
                    });
                }
            }
        }
    }

    // Individual row action functions
    function _confirmSingleRow(businessId) {
        _performSingleRowAction(businessId, 'confirm');
    }

    function _rejectSingleRow(businessId) {
        _performSingleRowAction(businessId, 'reject');
    }

    function _archiveSingleRow(businessId) {
        _performSingleRowAction(businessId, 'archive');
    }

    function _deleteSingleRow(businessId) {
        if (confirm('Sei sicuro di voler eliminare questa attività? Questa azione non può essere annullata.')) {
            _performSingleRowAction(businessId, 'delete');
        }
    }

    function _setPreferredSingleRow(businessId) {
        _performSingleRowAction(businessId, 'preferred');
    }

    function _setUnPreferredSingleRow(businessId) {
        _performSingleRowAction(businessId, 'unpreferred');
    }

    function _performSingleRowAction(businessId, operation) {
        const formData = new FormData();
        formData.append('businessIds', businessId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#business_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_BUSINESS_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                table.ajax.reload();
                new Noty({
                    text: 'Operazione completata correttamente.',
                    type: 'success',
                    closeWith: ['button']
                }).show();
            },
            error: function (error) {
                new Noty({
                    text: error.responseText || 'Errore durante l\'operazione',
                    type: 'error',
                    closeWith: ['button']
                }).show();
                console.error('Error during business operation', error);
            }
        });
    }

    // Make individual action functions globally accessible
    window._confirmSingleRow = _confirmSingleRow;
    window._rejectSingleRow = _rejectSingleRow;
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;
    window._setPreferredSingleRow = _setPreferredSingleRow;
    window._setUnPreferredSingleRow = _setUnPreferredSingleRow;

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
        },
        reloadTable: _reloadTable,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows
    };
}();


// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    BusinessCollection.init();
});
