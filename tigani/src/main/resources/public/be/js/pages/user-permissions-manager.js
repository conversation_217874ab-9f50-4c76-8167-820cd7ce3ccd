// User Permissions Manager JavaScript
const UserPermissionsManager = function () {
    
    let currentUserId = null;
    let currentUserName = null;

    // Initialize the page
    const _init = function () {
        _loadUsers();
        _setupEventHandlers();
    };

    // Load users into the dropdown
    const _loadUsers = function () {
        $.ajax({
            url: appRoutes.get("BE_USER_PERMISSIONS_MANAGER_USERS"),
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response && response.users) {
                    const selectInstance = HSSelect.getInstance('#user-select');
                    const currentValue = $('#user-select').val();

                    if (selectInstance) {
                        // Get current options (excluding the placeholder)
                        const currentOptions = $('#user-select option:not(:first)').map(function() {
                            return $(this).val();
                        }).get();

                        // Remove all current user options using Preline API
                        if (currentOptions.length > 0) {
                            selectInstance.removeOption(currentOptions);
                        }

                        // Add new user options using Preline API
                        const newOptions = response.users.map(function (user) {
                            const displayName = `${user.name} ${user.lastname}`;
                            return {
                                title: displayName,
                                val: user.id.toString(),
                                options: {
                                    description: user.email
                                }
                            };
                        });

                        selectInstance.addOption(newOptions);

                        // Restore selection if it existed
                        if (currentValue) {
                            selectInstance.setValue(currentValue);
                        }
                    }
                }
            },
            error: function (xhr, status, error) {
                console.error('Error loading users:', error);
                _showNotification('Errore nel caricamento degli utenti', 'error');
            }
        });
    };

    // Load permissions for selected user
    const _loadUserPermissions = function (userId, userName) {
        currentUserId = userId;
        currentUserName = userName;
        
        $('#selected-user-name').text(userName);
        $('#permissions-card').show();
        $('#permissions-loading').show();
        $('#permissions-container .permission-item').remove();

        $.ajax({
            url: appRoutes.get("BE_USER_PERMISSIONS"),
            type: 'GET',
            data: { userId: userId },
            dataType: 'json',
            success: function (response) {
                $('#permissions-loading').hide();
                
                if (response && response.permissions) {
                    _renderPermissions(response.permissions);
                } else if (response.error) {
                    _showNotification('Errore: ' + response.error, 'error');
                }
            },
            error: function (xhr, status, error) {
                $('#permissions-loading').hide();
                console.error('Error loading permissions:', error);
                _showNotification('Errore nel caricamento dei permessi', 'error');
            }
        });
    };

    // Render permissions cards
    const _renderPermissions = function (permissions) {
        const container = $('#permissions-container');
        const template = $('#permission-card-template')[0].content;

        permissions.forEach(function (permission) {
            const card = $(template.cloneNode(true));
            
            // Set permission info
            card.find('.permission-name').text(permission.name);
            card.find('.permission-description').text(permission.description);
            card.find('.permission-code').text(permission.code);
            
            // Set permission type checkboxes
            card.find('.permission-checkbox').each(function () {
                const checkbox = $(this);
                const permissionType = checkbox.data('permission-type');
                const isChecked = permission.userPermissions[permissionType] === true;
                
                checkbox.prop('checked', isChecked);
                checkbox.attr('data-permission-code', permission.code);
            });
            
            container.append(card);
        });
    };

    // Save all permissions
    const _saveAllPermissions = function () {
        if (!currentUserId) {
            _showNotification('Nessun utente selezionato', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('userId', currentUserId);

        // Collect all permission checkboxes
        $('#permissions-container .permission-checkbox').each(function () {
            const checkbox = $(this);
            const permissionCode = checkbox.data('permission-code');
            const permissionType = checkbox.data('permission-type');
            const isChecked = checkbox.is(':checked');
            
            const fieldName = `permission|${permissionCode}|${permissionType}`;
            formData.append(fieldName, isChecked ? 'true' : 'false');
        });

        // Show loading state
        const saveBtn = $('#save-all-permissions-btn');
        const originalText = saveBtn.html();
        saveBtn.prop('disabled', true).html(`
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Salvataggio...
        `);

        $.ajax({
            url: appRoutes.get("BE_USER_PERMISSIONS_SAVE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    _showNotification(response.message || 'Permessi salvati con successo', 'success');
                } else if (response.error) {
                    _showNotification('Errore: ' + response.error, 'error');
                }
            },
            error: function (xhr, status, error) {
                console.error('Error saving permissions:', error);
                _showNotification('Errore nel salvataggio dei permessi', 'error');
            },
            complete: function () {
                // Restore button state
                saveBtn.prop('disabled', false).html(originalText);
            }
        });
    };

    // Setup event handlers
    const _setupEventHandlers = function () {
        // User selection change
        $(document).on('change', '#user-select', function () {
            const userId = $(this).val();
            if (userId) {
                const selectedOption = $(this).find('option:selected');
                const userName = selectedOption.text();
                _loadUserPermissions(userId, userName);
            } else {
                $('#permissions-card').hide();
                currentUserId = null;
                currentUserName = null;
            }
        });

        // Save all permissions button
        $(document).on('click', '#save-all-permissions-btn', function (e) {
            e.preventDefault();
            _saveAllPermissions();
        });

        // Individual permission checkbox changes (for visual feedback)
        $(document).on('change', '.permission-checkbox', function () {
            const checkbox = $(this);
            const card = checkbox.closest('.permission-item');

            // Add visual feedback for changed state
            if (!card.hasClass('changed')) {
                card.addClass('changed').css('border-color', '#3b82f6');
            }
        });
    };

    // Show notification
    const _showNotification = function (message, type = 'info') {
        // Create notification element
        const notificationClass = type === 'success' ? 'bg-green-500' : 
                                 type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        
        const notification = $(`
            <div class="fixed top-4 right-4 z-50 ${notificationClass} text-white px-4 py-2 rounded-lg shadow-lg">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        // Auto remove after 3 seconds
        setTimeout(function () {
            notification.fadeOut(300, function () {
                $(this).remove();
            });
        }, 3000);
    };

    return {
        init: function () {
            _init();
        }
    };
}();

// Initialize when document is ready
$(document).ready(function () {
    UserPermissionsManager.init();
});
