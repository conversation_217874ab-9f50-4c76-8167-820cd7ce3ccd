const Settings = function () {
    // Initialization of components
    const init = function () {
        _componentFilePond();
        _componentSelect2();
        _componentDaterange();
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });


        // Format icon
        function iconFormat(icon) {
            if (!icon.id) {
                return icon.text;
            }
            var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;

            return $icon;
        }

        // Initialize with options
        $('.select-icons').select2({
            templateResult: iconFormat,
            minimumResultsForSearch: Infinity,
            templateSelection: iconFormat,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // Format country
        function formatLanguage(state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = pageVariables.get("contextPath") + '/be/images/lang/';
            var $state = $(
                    '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>');
            return $state;
        }
        ;

        $(".select-language").select2({
            templateResult: formatLanguage,
            templateSelection: formatLanguage
        });

    };

    // Daterange picker
    const _componentDaterange = function () {
        if (!$().daterangepicker) {
            console.warn('Warning - daterangepicker.js is not loaded.');
            return;
        }

        // Single picker
        $('.daterange-single').daterangepicker({
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            drops: 'up',
            startDate: $('.daterange-single').val() || moment().format('DD/MM/YYYY'),
            parentEl: '.content-inner',
            singleDatePicker: true,
            autoApply: true
        });

    };    

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // custom url validation
        $.validator.addMethod('identifier', function (value) {
            return /^[a-z0-9-_]+$/.test(value);
        }, 'URL non valido. L\'identificatore deve contenere solo lettere minuscole, numeri, trattini e sottolineature.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        const deleteBtn = document.getElementById('delete-user-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                const userId = $(this).data('userid');
                if (!userId) {
                    showToast('Errore: ID utente non trovato', 'error');
                    return;
                }
                // Show confirmation dialog
                $.confirm({
                    title: 'Conferma eliminazione',
                    content: 'Sei sicuro di voler eliminare questo utente? Questa azione non può essere annullata.',
                    type: 'red',
                    typeAnimated: true,
                    buttons: {
                        elimina: {
                            text: 'Elimina',
                            btnClass: 'btn-red',
                            action: function () {
                                if (userId) {
                                    // Call delete operation
                                    const formData = new FormData();
                                    formData.append('userIds', userId);
                                    formData.append('operation', 'delete');

                                    $.blockUI();
                                    $.ajax({
                                        url: appRoutes.get('BE_USER_OPERATE'),
                                        type: 'POST',
                                        data: formData,
                                        processData: false,
                                        contentType: false,
                                        success: function(response) {
                                            $.unblockUI();

                                            // Close offcanvas
                                            const offcanvasElement = deleteBtn.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                                            if (offcanvasElement) {
                                                const overlay = HSOverlay.getInstance(offcanvasElement, true);
                                                if (overlay) {
                                                    overlay.element.close();
                                                }
                                            }

                                            // Reload table
                                            if (window.usersDataTable && window.usersDataTable.dataTable) {
                                                window.usersDataTable.dataTable.ajax.reload();
                                            }

                                            // Show success message
                                            showToast('Utente eliminato correttamente', 'success');
                                        },
                                        error: function(xhr, status, error) {
                                            $.unblockUI();
                                            let errorMessage = 'Errore durante l\'eliminazione';
                                            if (xhr.responseText) {
                                                errorMessage = xhr.responseText;
                                            }
                                            showToast(errorMessage, 'error');
                                            console.error('Error during user deletion:', error);
                                        }
                                    });
                                }
                            }
                        },
                        annulla: {
                            text: 'Annulla',
                            btnClass: 'btn-light'
                        }
                    }
                });
            });
        }
    }

    // FilePond
    const _componentFilePond = function () {

        // Social share preview

        FilePond.registerPlugin(
                FilePondPluginImageExifOrientation,
                FilePondPluginImagePreview,
                FilePondPluginImageCrop,
                FilePondPluginImageResize,
                FilePondPluginImageFilter,
                FilePondPluginImageTransform,
                FilePondPluginImageEdit,
                FilePondPluginImageValidateSize,
                FilePondPluginFileEncode,
                FilePondPluginFileValidateType,
                FilePondPluginFileValidateSize
                );
        FilePond.setOptions(FilePondIT);

        // Support both regular form and offcanvas form
        const inputElement = document.querySelector('input[type="file"]') || document.querySelector('#logo-offcanvas');

        const doka = dokaCreate({
            //utils: 'crop, color',
            cropAspectRatioOptions: [
                {
                    label: 'Avatar',
                    value: 1 / 1
                }
            ],
            crop: {
                aspectRatio: 1 / 1
            },
            labelButtonReset: "Reimposta",
            labelButtonCancel: "Annulla",
            labelButtonConfirm: "Conferma",
            labelButtonUtilCrop: "Ritaglia",
            labelButtonUtilResize: "Ridimensiona",
            labelButtonUtilFilter: "Filtra",
            labelButtonUtilColor: "Colori",
            labelButtonUtilMarkup: "Annota",
            labelStatusMissingWebGL: "WebGL è richiesto ma è disabilitato nel tuo browser",
            labelStatusAwaitingImage: "In attesa dell'immagine…",
            labelStatusLoadImageError: "Errore nel caricamento dell'immagine…",
            labelStatusLoadingImage: "Caricamento dell'immagine…",
            labelStatusProcessingImage: "Elaborazione dell'immagine…",
            labelColorBrightness: "Luminosità",
            labelColorContrast: "Contrasto",
            labelColorExposure: "Esposizione",
            labelColorSaturation: "Saturazione",
            labelMarkupTypeRectangle: "Rettangolo",
            labelMarkupTypeEllipse: "Cerchio",
            labelMarkupTypeText: "Testo",
            labelMarkupTypeLine: "Linea",
            labelMarkupSelectFontSize: "Dimensione",
            labelMarkupSelectFontFamily: "Carattere",
            labelMarkupSelectLineDecoration: "Decorazione",
            labelMarkupSelectLineStyle: "Stile",
            labelMarkupSelectShapeStyle: "Stile",
            labelMarkupRemoveShape: "Rimuovi",
            labelMarkupToolSelect: "Seleziona",
            labelMarkupToolDraw: "Disegna",
            labelMarkupToolLine: "Linea",
            labelMarkupToolText: "Testo",
            labelMarkupToolRect: "Rettangolo",
            labelMarkupToolEllipse: "Cerchio",
            labelResizeWidth: "Larghezza",
            labelResizeHeight: "Altezza",
            labelResizeApplyChanges: "Applica modifiche",
            labelCropInstructionZoom: "Zoom avanti e indietro con la rotellina del mouse o il touchpad.",
            labelButtonCropZoom: "Zoom",
            labelButtonCropRotateLeft: "Ruota a sinistra",
            labelButtonCropRotateRight: "Ruota a destra",
            labelButtonCropRotateCenter: "Centra rotazione",
            labelButtonCropFlipHorizontal: "Rifletti orizzontalmente",
            labelButtonCropFlipVertical: "Rifletti verticalmente",
            labelButtonCropAspectRatio: "Proporzioni",
            labelButtonCropToggleLimit: "Selezione ritaglio",
            labelButtonCropToggleLimitEnable: "Limitato all'immagine",
            labelButtonCropToggleLimitDisable: "Seleziona fuori immagine",
            pointerEventsPolyfillScope: "ambito",
            styleCropCorner: "angolo",
            styleFullscreenSafeArea: "area sicura a schermo intero"

        })

        pond = FilePond.create(inputElement, {

            allowFileEncode: true,
            allowImagePreview: true,
            allowImageTransform: true,
            allowImageEdit: true,
            allowImageCrop: true,
            stylePanelLayout: 'compact circle',
            stylePanelAspectRatio: '1:1',
            styleLoadIndicatorPosition: 'center bottom',
            styleProgressIndicatorPosition: 'right bottom',
            styleButtonRemoveItemPosition: 'left bottom',
            styleButtonProcessItemPosition: 'right bottom',            
            imageCropAspectRatio: '1:1',
            imagePreviewHeight: 200,            
            imageResizeTargetWidth: 400,
            imageResizeTargetHeight: 400,            
            allowImageResize: true,
            allowImageExifOrientation: true,
            maxFileSize: '3MB',
            acceptedFileTypes: ['image/png', 'image/jpeg', 'image/svg'],
            imageEditEditor: doka
        });

        doka.on('confirm', (output) => {
            console.log(output);
        });

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + pageVariables.get("imageId").replace("[", "").replace("]", "");
            pond.addFile(image);
        }
    };
    
    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------
document.addEventListener('DOMContentLoaded', function () {
    Settings.init();  
    submitUser();

});

function submitUser() {
    // Form submission handling
    $('#user-edit').submit(function (e) {
        if ($('#user-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);
            // Supponendo che tu voglia inviare solo il primo file croppato
            if (pond.getFiles().length > 0) {
                // Ottieni la stringa base64 del file croppato
                const fileToInsert = pond.getFiles()[0];
                const base64String = fileToInsert.getFileEncodeBase64String();
                const mimeType = fileToInsert.fileType;
                const blob = base64ToBlob(base64String, mimeType);
                const fileName = fileToInsert.filename;
                const file = new File([blob], fileName, {type: mimeType});

                // Aggiungi il file croppato al FormData
                formData.append('file', file);
            }

            // formData.append('language', "en");
            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_USER") + "?userId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during article save/update', error);
                }
            });
        }
    });
}

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}