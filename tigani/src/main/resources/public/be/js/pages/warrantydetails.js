const WarrantyDetailsSettings = function () {
    // Initialization of components
    const init = function () {
        _componentSelect2();
        _componentValidate();
        _componentMaxlength();
    };

    // Select2
    const _componentSelect2 = function () {
        if (!$().select2) {
            console.warn('Warning - select2.min.js is not loaded.');
            return;
        }

        // Default initialization
        $('.select').select2({
            language: "it"
        });


        // Format icon
        function iconFormat(icon) {
            if (!icon.id) {
                return icon.text;
            }
            var $icon = '<i class="ph-' + $(icon.element).data('icon') + '"></i>' + icon.text;

            return $icon;
        }

        // Initialize with options
        $('.select-icons').select2({
            templateResult: iconFormat,
            minimumResultsForSearch: Infinity,
            templateSelection: iconFormat,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // Format country
        function formatLanguage(state) {
            if (!state.id) {
                return state.text;
            }
            var baseUrl = pageVariables.get("contextPath") + '/be/images/lang/';
            var $state = $(
                    '<span><img src="' + baseUrl + '/' + state.element.value.toLowerCase() + '.svg" class="img-flag" /> ' + state.text + '</span>');
            return $state;
        }
        ;

        $(".select-language").select2({
            templateResult: formatLanguage,
            templateSelection: formatLanguage
        });

    };

    // Validation
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Add custom validation methods
        $.validator.addMethod("greaterThanOrEqual", function(value, element, param) {
            if (!value || !$(param).val()) {
                return true; // Skip validation if either field is empty
            }
            return parseInt(value) >= parseInt($(param).val());
        }, "Il valore 'A' deve essere maggiore o uguale al valore 'Da'");

        // Initialize
        const validator = $('.form-validate').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            success: function(label) {
                label.addClass('validation-valid-label').text('Success.'); // remove to hide Success message
            },

            // Different components require proper error label placement
            errorPlacement: function(error, element) {

                // Unstyled checkboxes, radios
                if (element.parents().hasClass('form-check')) {
                    error.appendTo( element.parents('.form-check').parent() );
                }

                // Input with icons and Select2
                else if (element.parents().hasClass('form-group-feedback') || element.hasClass('select2-hidden-accessible')) {
                    error.appendTo( element.parent() );
                }

                // Input group, styled file input
                else if (element.parent().is('.uniform-uploader, .uniform-select') || element.parents().hasClass('input-group')) {
                    error.appendTo( element.parent().parent() );
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            },
            rules: {
                warrantyId: {
                    required: true
                },
                claimNumberFrom: {
                    min: 0,
                    digits: true
                },
                claimNumberTo: {
                    min: 0,
                    digits: true,
                    greaterThanOrEqual: "input[name='claimNumberFrom']"
                },
                universalClassFrom: {
                    min: 1,
                    max: 18,
                    digits: true
                },
                universalClassTo: {
                    min: 1,
                    max: 18,
                    digits: true,
                    greaterThanOrEqual: "input[name='universalClassFrom']"
                },
                premiumValue: {
                    min: 0
                }
            }
        });

        // Reset form
        $('#reset').on('click', function() {
            validator.resetForm();
        });
    };

    // Maxlength
    const _componentMaxlength = function() {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.form-control-maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: "badge bg-warning",
            limitReachedClass: "badge bg-danger"
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
document.addEventListener('DOMContentLoaded', function () {
    WarrantyDetailsSettings.init();
    submitWarrantyDetails();
    initWarrantyFieldsVisibility();
});

function submitWarrantyDetails() {
    // Form submission handling
    $('#warrantydetails-edit').submit(function (e) {
        if ($('#warrantydetails-edit').valid()) {
            e.preventDefault();
            const formData = new FormData(this);

            $.blockUI();
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    var url = appRoutes.get("BE_WARRANTYDETAILS") + "?warrantyDetailsId=" + response.toString();
                    Swal.fire({
                        text: 'Dati salvati correttamente',
                        icon: 'success',
                        timer: 1000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    }).then(function () {
                        $.unblockUI();
                        window.location.href = url;
                    });
                },
                error: function (error) {
                    // Handle errors
                    $.unblockUI();
                    Swal.fire({
                        text: error.responseText,
                        icon: 'error',
                        timer: 3000,
                        toast: true,
                        showConfirmButton: false,
                        position: 'top-end'
                    });
                    console.error('Error during warranty details save/update', error);
                }
            });
        }
    });
}

function initWarrantyFieldsVisibility() {
    const warrantySelect = $('#warrantySelect');
    const allFields = ['provinceCode', 'claimNumber', 'insuranceProvenanceTypeId', 'universalClass'];

    // Handle preselected warranty ID from URL parameter
    if (typeof window.preselectedWarrantyId !== 'undefined' && window.preselectedWarrantyId) {
        warrantySelect.val(window.preselectedWarrantyId);
        warrantySelect.prop('readonly', true);
        warrantySelect.addClass('readonly-select');

        // Add CSS to make it look readonly
        if (!$('#readonly-select-style').length) {
            $('<style id="readonly-select-style">.readonly-select { background-color: #f8f9fa !important; pointer-events: none; }</style>').appendTo('head');
        }

        // Load criteria fields for preselected warranty
        loadWarrantyCriteriaFields(window.preselectedWarrantyId);
    }

    // Handle warranty selection change
    warrantySelect.on('change', function() {
        const selectedWarrantyId = $(this).val();

        if (selectedWarrantyId) {
            loadWarrantyCriteriaFields(selectedWarrantyId);
        } else {
            // Show all fields if no warranty is selected
            showAllFields(allFields);
        }
    });

    // Initial load - if warranty is already selected (edit mode)
    const currentWarrantyId = warrantySelect.val();
    if (currentWarrantyId && !window.preselectedWarrantyId) {
        loadWarrantyCriteriaFields(currentWarrantyId);
    }
}

function loadWarrantyCriteriaFields(warrantyId) {
    if (!warrantyId) return;

    $.ajax({
        url: appRoutes.get('BE_WARRANTY_CRITERIA'),
        type: 'GET',
        data: { warrantyId: warrantyId },
        success: function(criteriaFields) {
            updateFieldsVisibility(criteriaFields);
        },
        error: function(xhr, status, error) {
            console.error('Error loading warranty criteria fields:', error);
            // Show all fields on error
            const allFields = ['provinceCode', 'claimNumber', 'insuranceProvenanceTypeId', 'universalClass'];
            showAllFields(allFields);
        }
    });
}

function updateFieldsVisibility(criteriaFields) {
    const allFields = ['provinceCode', 'claimNumber', 'insuranceProvenanceTypeId', 'universalClass'];

    // Hide all fields first
    allFields.forEach(function(fieldName) {
        const fieldElement = $('[data-field="' + fieldName + '"]');
        fieldElement.hide();
    });

    // Show only the fields specified in criteriaFields
    if (criteriaFields && criteriaFields.length > 0) {
        criteriaFields.forEach(function(fieldName) {
            const fieldElement = $('[data-field="' + fieldName + '"]');
            fieldElement.show();
        });
    } else {
        // If no criteria fields specified, show all fields
        showAllFields(allFields);
    }
}

function showAllFields(allFields) {
    allFields.forEach(function(fieldName) {
        const fieldElement = $('[data-field="' + fieldName + '"]');
        fieldElement.show();
    });
}
