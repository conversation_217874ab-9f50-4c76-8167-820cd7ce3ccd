{% extends "be/include/preline-base.html" %}

{% block extrahead %}
<title>Manutenzione / Permessi</title>
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_PERMISSION_DATA', '{{ routes("BE_PERMISSION_DATA") }}');
    addRoute('BE_PERMISSION_OPERATE', '{{ routes("BE_PERMISSION_OPERATE") }}');
    addRoute('BE_PERMISSION_FORM', '{{ routes("BE_PERMISSION_FORM") }}');
    addRoute('BE_PERMISSION_SAVE', '{{ routes("BE_PERMISSION_SAVE") }}');
    addRoute('BE_PERMISSION_INITIALIZE', '{{ routes("BE_PERMISSION_INITIALIZE") }}');
</script>

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card con DataTable -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
            <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    Lista Permessi
                </h2>
            </div>
            <div class="flex flex-wrap items-center gap-2">
                <!-- Initialize Permissions Button -->
                <button type="button" id="initialize-permissions-btn" class="py-1.5 px-2.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2v20m8-10H4"/>
                    </svg>
                    Inizializza Permessi
                </button>
                
                <!-- Create Permission Button -->
                <button type="button" id="create-permission-btn" class="py-1.5 px-2.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2v20m8-10H4"/>
                    </svg>
                    Nuovo Permesso
                </button>
            </div>
        </div>

        <!-- DataTable Content -->
        <div class="p-5">
            <div class="flex flex-col">
                <div id="permission-datatable-container">
                    <div class="flex flex-wrap items-center gap-2 mb-4">
                        <div class="grow">
                            <div class="relative max-w-xs w-full">
                                <label for="hs-table-filter-search" class="sr-only">Search</label>
                                <input type="text" name="hs-table-filter-search" id="hs-table-filter-search" class="py-1.5 sm:py-2 px-3 ps-9 block w-full border-gray-200 shadow-2xs rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Cerca..." data-hs-datatable-search="">
                                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
                                    <svg class="size-4 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <path d="m21 21-4.35-4.35"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="flex items-center gap-2">
                            <button type="button" id="bulk-archive-btn" class="py-1.5 px-2.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" disabled>
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18l-2 13H5L3 6Z"/>
                                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                </svg>
                                Archivia
                            </button>
                            
                            <button type="button" id="bulk-delete-btn" class="py-1.5 px-2.5 inline-flex items-center gap-x-1 text-xs font-medium rounded-lg border border-gray-200 bg-white text-red-500 shadow-xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-400 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" disabled>
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18"/>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c0 1 1 2 2 2v2"/>
                                </svg>
                                Elimina
                            </button>
                        </div>

                        <!-- Date Range Picker -->
                        <div class="relative">
                            <input type="text" id="daterange-picker" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 shadow-2xs rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Seleziona periodo..." readonly>
                        </div>
                    </div>

                    <!-- DataTable -->
                    <div class="overflow-hidden">
                        <table id="permissions-table" class="min-w-full divide-y divide-gray-200 dark:divide-neutral-700" data-hs-datatable='{
                            "pageLength": 10,
                            "pagingType": "simple_numbers",
                            "searching": true,
                            "ordering": true,
                            "info": true,
                            "lengthChange": true,
                            "responsive": true,
                            "select": {
                                "style": "multi",
                                "selector": "td:select-checkbox"
                            },
                            "columnDefs": [
                                {
                                    "targets": 0,
                                    "orderable": false,
                                    "searchable": false,
                                    "checkboxes": true
                                },
                                {
                                    "targets": -1,
                                    "orderable": false,
                                    "searchable": false
                                }
                            ],
                            "order": [[1, "asc"]],
                            "language": {
                                "search": "",
                                "searchPlaceholder": "Cerca...",
                                "lengthMenu": "Mostra _MENU_ elementi",
                                "info": "Mostra da _START_ a _END_ di _TOTAL_ elementi",
                                "infoEmpty": "Mostra da 0 a 0 di 0 elementi",
                                "infoFiltered": "(filtrato da _MAX_ elementi totali)",
                                "paginate": {
                                    "first": "Primo",
                                    "last": "Ultimo",
                                    "next": "Successivo",
                                    "previous": "Precedente"
                                },
                                "emptyTable": "Nessun dato disponibile nella tabella",
                                "zeroRecords": "Nessun record corrispondente trovato"
                            }
                        }'>
                            <thead class="bg-gray-50 dark:bg-neutral-800">
                                <tr>
                                    <th scope="col" class="ps-6 py-3 text-start">
                                        <div class="flex items-center gap-x-2">
                                            <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                                Seleziona
                                            </span>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-start">
                                        <div class="flex items-center gap-x-2">
                                            <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                                Codice
                                            </span>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-start">
                                        <div class="flex items-center gap-x-2">
                                            <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                                Nome
                                            </span>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-start">
                                        <div class="flex items-center gap-x-2">
                                            <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                                Descrizione
                                            </span>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-start">
                                        <div class="flex items-center gap-x-2">
                                            <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                                Creazione
                                            </span>
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-end">
                                        <span class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                                            Azioni
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Offcanvas for Create/Edit Permission -->
<div id="permission-offcanvas" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform h-full max-w-md w-full z-[80] bg-white border-s dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1" aria-labelledby="permission-offcanvas-label">
    <!-- Header -->
    <div class="flex justify-between items-center py-3 px-4 border-b dark:border-neutral-700">
        <h3 id="permission-offcanvas-label" class="font-semibold text-gray-800 dark:text-white">
            Gestione Permesso
        </h3>
        <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-600 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#permission-offcanvas">
            <span class="sr-only">Close</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        </button>
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="bg-gray-100 h-full overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-800">
        <div id="permission-form-content">
            <!-- Form content will be loaded here -->
        </div>
    </div>
</div>

<!-- Archived Filter Offcanvas -->
<div id="archived-filter-offcanvas" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform h-full max-w-xs w-full z-[80] bg-white border-s dark:bg-neutral-800 dark:border-neutral-700" role="dialog" tabindex="-1">
    <!-- Header -->
    <div class="flex justify-between items-center py-3 px-4 border-b dark:border-neutral-700">
        <h3 class="font-semibold text-gray-800 dark:text-white">
            Filtri
        </h3>
        <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-600 dark:focus:bg-neutral-600" aria-label="Close" data-hs-overlay="#archived-filter-offcanvas">
            <span class="sr-only">Close</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        </button>
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="bg-gray-100 h-full overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-800">
        <div class="p-2 space-y-2">
            <!-- Archived Filter Card -->
            <div class="p-4 bg-white rounded-lg shadow-2xs dark:bg-neutral-900">
                <div class="mb-3">
                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">Opzioni di visualizzazione</span>
                </div>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="archived-filter" value="active" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" checked>
                        <span class="text-sm text-gray-500 ms-2 dark:text-neutral-400">Solo attivi</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="archived-filter" value="archived" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800">
                        <span class="text-sm text-gray-500 ms-2 dark:text-neutral-400">Solo archiviati</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block pagescript %}
<!-- Date Range Picker -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Form Plugins for Offcanvas -->
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/select2.html" %}

<!-- Page Scripts -->
<script src="{{ contextPath }}/be/js/pages/permission-collection.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/pages/permission-form.js?{{ buildNumber }}"></script>
{% endblock %}
