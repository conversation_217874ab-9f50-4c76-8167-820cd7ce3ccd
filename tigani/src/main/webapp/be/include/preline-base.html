<!DOCTYPE html>
<html lang="it" dir="ltr" class="relative min-h-full">
    <head>
        <!-- Meta -->
        <meta charset="utf-8">        
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">                       
        
        <!-- Favicon -->
        <link rel="shortcut icon" href="../favicon.ico">

        <!-- Font -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- CSS -->
        <link href="{{ contextPath }}/default/css/main.min.css" rel="stylesheet" type="text/css">
        <!--<script src="https://cdn.tailwindcss.com"></script>-->

        <!-- Jquery -->
        <script src="{{ contextPath }}/default/js/jquery.min.js"></script>

        <!-- Datatable -->
        <script src="{{ contextPath }}/default/js/dataTables.min.js"></script>
        <script src="{{ contextPath }}/be/js/dataTables.buttons.min.js"></script>
        <script src="{{ contextPath }}/be/js/jszip.min.js"></script>
        <script src="{{ contextPath }}/be/js/pdfmake.min.js"></script>
        <script src="{{ contextPath }}/be/js/vfs_fonts.js"></script>
        <script src="{{ contextPath }}/be/js/buttons.html5.min.js"></script>
        <script src="{{ contextPath }}/be/js/buttons.print.min.js"></script>

        <!-- Toastify -->
        <!--<link href="{{ contextPath }}/be/css/toastify.css" rel="stylesheet" type="text/css">-->
        <script src="{{ contextPath }}/be/js/toastify.js"></script>

        <!-- JS VENDOR -->
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/ui/moment/moment.min.js"></script>
        <script src="https://anubi.us/static/themes/b/1/4.0/assets/js/vendor/ui/moment/moment_locales.min.js"></script>
        {% include "be/include/snippets/plugins/blockui.html" %}
        <script src="{{ contextPath }}/be/js/vendor/tables/datatables/datetime-moment.js"></script>
        <script src="{{ contextPath }}/default/js/lodash.min.js"></script>
        <script src="{{ contextPath }}/default/js/vanilla-calendar-pro.min.js"></script>

        <!-- Jquery Confirm -->
        <script src="{{ contextPath }}/be/js/jquery-confirm.min.js"></script>
        <link rel="stylesheet" href="{{ contextPath }}/be/css/jquery-confirm.min.css">

        <!-- Custom -->
        <script src="{{ contextPath }}/be/js/custom.js"></script>
        <link href="{{ contextPath }}/be/css/custom.css" rel="stylesheet" type="text/css">

        <!-- Theme Check and Update -->
        <script>
          const html = document.querySelector('html');
          const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
          const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

          if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
          else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
          else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
          else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>
       
        {% block extrahead %}{% endblock %}
    </head>

    <body class="bg-gray-50 dark:bg-neutral-900">
        
        <!-- ========== HEADER ========== -->
        {% include "be/include/snippets/preline-header.html" %}
        <!-- ========== END HEADER ========== -->

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content" class="pt-15">
          <!-- Container -->
          <div class="max-w-full mx-auto">            
            <!-- Content -->
            
                <!-- ========== MAIN SIDEBAR ========== -->
                {% include "be/include/snippets/preline-sidebar.html" %}
                <!-- ========== END MAIN SIDEBAR ========== -->
                
                {% block content %}{% endblock %}
            
        </main>

        {% block pagescript %}{% endblock %}
        <script src="{{ contextPath }}/default/js/preline.min.js"></script>
    </body>

</html>