<!-- Permission Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_PERMISSION', '{{ routes("BE_PERMISSION") }}');
    addRoute('BE_PERMISSION_SAVE', '{{ routes("BE_PERMISSION_SAVE") }}');
    addRoute('BE_PERMISSION_OPERATE', '{{ routes("BE_PERMISSION_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_PERMISSION_SAVE') %}
            {% if curPermission.id is not empty %}                
            {% set postUrl = routes('BE_PERMISSION_SAVE') + '?permissionId=' + curPermission.id %}
            {% endif %}

            <form id="permission-form" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <!-- Code Field -->
                <div class="mb-4">
                    <label for="code" class="block text-sm font-medium mb-2 dark:text-white">
                        Codice <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="code" name="code" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" 
                           placeholder="Es: USER_MANAGEMENT" 
                           value="{{ curPermission.code }}" 
                           required
                           {% if curPermission.id is not empty %}readonly{% endif %}>
                    <p class="text-xs text-gray-500 mt-2">Codice univoco del permesso (non modificabile dopo la creazione)</p>
                </div>

                <!-- Name Field -->
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium mb-2 dark:text-white">
                        Nome <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="name" name="name" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" 
                           placeholder="Es: Gestione Utenti" 
                           value="{{ curPermission.name }}" 
                           required>
                    <p class="text-xs text-gray-500 mt-2">Nome descrittivo del permesso</p>
                </div>

                <!-- Description Field -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium mb-2 dark:text-white">
                        Descrizione <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" name="description" rows="4" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" 
                              placeholder="Descrizione dettagliata del permesso e delle azioni che consente..." 
                              required>{{ curPermission.description }}</textarea>
                    <p class="text-xs text-gray-500 mt-2">Descrizione dettagliata delle funzionalità coperte da questo permesso</p>
                </div>

                {% if curPermission.id is not empty %}
                <!-- Permission Info -->
                <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Informazioni Permesso</h4>
                    <div class="text-xs text-blue-600 dark:text-blue-300 space-y-1">
                        <div><strong>ID:</strong> {{ curPermission.id }}</div>
                        {% if curPermission.creation %}
                        <div><strong>Creato:</strong> {{ curPermission.creation | date('dd/MM/yyyy HH:mm') }}</div>
                        {% endif %}
                        {% if curPermission.lastUpdate %}
                        <div><strong>Ultima modifica:</strong> {{ curPermission.lastUpdate | date('dd/MM/yyyy HH:mm') }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Permission Types Info -->
                <div class="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700">
                    <h4 class="text-sm font-medium text-gray-800 dark:text-neutral-200 mb-2">Tipi di Permesso Disponibili</h4>
                    <div class="text-xs text-gray-600 dark:text-neutral-400 space-y-1">
                        <div><strong>VIEW:</strong> Visualizzazione dei dati</div>
                        <div><strong>CREATE:</strong> Creazione di nuovi elementi</div>
                        <div><strong>EDIT:</strong> Modifica di elementi esistenti</div>
                        <div><strong>DELETE:</strong> Eliminazione di elementi</div>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-neutral-500 mt-2">
                        Questi tipi vengono assegnati agli utenti per definire le azioni specifiche che possono compiere.
                    </p>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex justify-end items-center gap-x-2 py-3 px-4 border-t dark:border-neutral-700">
        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-xs hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#permission-offcanvas">
            Annulla
        </button>
        <button type="submit" form="permission-form" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                <polyline points="17,21 17,13 7,13 7,21"></polyline>
                <polyline points="7,3 7,8 15,8"></polyline>
            </svg>
            Salva
        </button>
    </div>
</div>
